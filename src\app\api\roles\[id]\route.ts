import { NextRequest } from 'next/server';
import { query, queryOne } from '@/lib/database';
import { createApiResponse, createApiError, withRole } from '@/lib/utils';

// 仅超级管理员可管理角色
export const PUT = withRole('super_admin')(async (request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    const roleId = parseInt(params.id);
    const body = await request.json();
    const { name, permissionNames } = body as { name?: string; permissionNames?: string[] };

    const existing = await queryOne('SELECT id, name FROM roles WHERE id = ?', [roleId]);
    if (!existing) {
      return createApiError('ROLE_NOT_FOUND', 'Role not found', null, 404);
    }

    if (typeof name === 'string' && name.trim()) {
      await query('UPDATE roles SET name = ? WHERE id = ?', [name.trim(), roleId]);
    }

    if (Array.isArray(permissionNames)) {
      // 重置并写入新的权限集
      await query('DELETE FROM role_permissions WHERE role_id = ?', [roleId]);
      if (permissionNames.length > 0) {
        const placeholders = permissionNames.map(() => '?').join(',');
        const permissions = await query(`SELECT id, name FROM permissions WHERE name IN (${placeholders})`, permissionNames);
        if (permissions.length > 0) {
          const values: any[] = [];
          const tuples = permissions.map((p: any) => { values.push(roleId, p.id); return '(?, ?)'; }).join(',');
          await query(`INSERT INTO role_permissions (role_id, permission_id) VALUES ${tuples}`, values);
        }
      }
    }

    const updated = await queryOne('SELECT id, name FROM roles WHERE id = ?', [roleId]);
    return createApiResponse(true, updated, 'Role updated');
  } catch (error) {
    console.error('Update role error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'Failed to update role', error, 500);
  }
});

export const DELETE = withRole('super_admin')(async (request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    const roleId = parseInt(params.id);
    const existing = await queryOne('SELECT id, name FROM roles WHERE id = ?', [roleId]);
    if (!existing) {
      return createApiError('ROLE_NOT_FOUND', 'Role not found', null, 404);
    }

    // 防御：禁止删除超级管理员角色
    if (existing.name === 'super_admin') {
      return createApiError('FORBIDDEN', 'Cannot delete super_admin role', null, 403);
    }

    // 防御：角色若仍有关联用户，禁止删除
    const used = await queryOne('SELECT COUNT(*) AS cnt FROM users WHERE role_id = ?', [roleId]);
    if (used && used.cnt > 0) {
      return createApiError('ROLE_IN_USE', 'Role is assigned to users and cannot be deleted', null, 400);
    }

    await query('DELETE FROM role_permissions WHERE role_id = ?', [roleId]);
    await query('DELETE FROM roles WHERE id = ?', [roleId]);
    return createApiResponse(true, { id: roleId }, 'Role deleted');
  } catch (error) {
    console.error('Delete role error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'Failed to delete role', error, 500);
  }
});

