import useSWR, { mutate as swrMutate } from 'swr';

// 通用fetcher函数 - 支持认证
const fetcher = async (url: string) => {
  const res = await fetch(url, {
    method: 'GET',
    credentials: 'include', // 包含cookies
    headers: {
      'Content-Type': 'application/json',
    }
  });
  if (!res.ok) {
    throw new Error('Failed to fetch data');
  }
  return res.json();
};

// 网站数据Hook
export function useWebsites(page = 1, limit = 10, search = '', category = '') {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    search,
  });
  
  if (category) {
    params.append('category', category);
  }

  const { data, error, isLoading, mutate } = useSWR(
    `/api/links?${params.toString()}`,
    fetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      refreshInterval: 60000, // 1分钟自动刷新
      dedupingInterval: 5000, // 5秒内的重复请求去重
      errorRetryCount: 3,
      revalidateIfStale: true, // 强制重新验证过期数据
    }
  );

  // 调试日志
  console.log('=== useWebsites Hook Debug ===');
  console.log('Raw SWR data:', data);
  console.log('data?.success:', data?.success);
  console.log('data?.data:', data?.data);
  console.log('data?.data?.links:', data?.data?.links);

  // 构建返回对象
  const returnData = {
    websites: {
      websites: data?.data?.links || [],
      total: data?.data?.total || 0,
      page: data?.data?.page || 1,
      totalPages: data?.data?.totalPages || 0
    },
    isLoading,
    error,
    mutate
  };
  
  // 调试返回数据结构
  console.log('=== useWebsites Hook Return Debug ===');
  console.log('Return object structure:', JSON.stringify({
    websites: {
      isObject: typeof returnData.websites === 'object',
      hasWebsitesArray: Array.isArray(returnData.websites.websites),
      websitesLength: returnData.websites.websites.length,
      total: returnData.websites.total,
      page: returnData.websites.page,
      totalPages: returnData.websites.totalPages
    },
    isLoading: returnData.isLoading,
    hasError: !!returnData.error
  }, null, 2));
  console.log('Actual return data:', returnData);

  return returnData;
}

// 分类数据Hook
export function useCategories(limit = 100) {
  const { data, error, isLoading, mutate } = useSWR(
    `/api/categories?limit=${limit}`,
    fetcher,
    {
      revalidateOnFocus: false,
      refreshInterval: 300000, // 5分钟刷新
      dedupingInterval: 10000, // 10秒去重
    }
  );

  return {
    categories: data?.data?.categories || [],
    isLoading,
    error,
    mutate
  };
}

// 分类分页搜索Hook（用于管理页面）
export function useCategoriesWithPagination(page = 1, limit = 10, search = '') {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    search,
  });

  const { data, error, isLoading, mutate } = useSWR(
    `/api/categories?${params.toString()}`,
    fetcher,
    {
      revalidateOnFocus: false,
      refreshInterval: 0, // 实时排序反馈，取消定时刷新
      dedupingInterval: 0, // 禁止5秒去重，确保每次mutate都命中revalidate
      revalidateIfStale: true,
    }
  );

  return {
    categories: {
      categories: data?.data?.categories || [],
      total: data?.data?.pagination?.total || 0,
      page: data?.data?.pagination?.page || 1,
      totalPages: data?.data?.pagination?.totalPages || 0
    },
    isLoading,
    error,
    // 强制刷新函数：每次操作后获取最新列表，绕过短期去重
    mutate: (data?: any, opts?: any) => mutate(data, { revalidate: true, populateCache: true, ...opts })
  };
}

// 用户数据Hook  
export function useUsers(page = 1, limit = 10, search = '') {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    search,
  });

  const { data, error, isLoading, mutate } = useSWR(
    `/api/users?${params.toString()}`,
    fetcher,
    {
      revalidateOnFocus: false,
      refreshInterval: 120000, // 2分钟刷新
      dedupingInterval: 5000,
    }
  );

  return {
    users: {
      users: data?.data?.users || [],
      total: data?.data?.pagination?.total || 0,
      page: data?.data?.pagination?.page || 1,
      totalPages: data?.data?.pagination?.totalPages || 0
    },
    isLoading,
    error,
    mutate
  };
}

// 仪表板统计数据Hook
export function useDashboardStats(timeRange = 30) {
  const { data, error, isLoading } = useSWR(
    ['/api/statistics', timeRange],
    () => fetchDashboardStats(timeRange),
    {
      refreshInterval: 60000, // 1分钟刷新
      revalidateOnFocus: false,
    }
  );

  return {
    stats: data || null,
    isLoading,
    error
  };
}

// 获取仪表板统计数据
async function fetchDashboardStats(timeRange: number) {
  const [
    overviewRes,
    userGrowthRes,
    categoryRes,
    visitRes,
    activityRes,
    trendRes,
    heatmapRes
  ] = await Promise.all([
    fetch('/api/statistics?type=overview'),
    fetch(`/api/statistics?type=userGrowth&days=${timeRange}`),
    fetch('/api/statistics?type=categoryStats'),
    fetch('/api/statistics?type=visitStats'),
    fetch('/api/statistics?type=activityStats'),
    fetch(`/api/statistics?type=trendStats&days=${timeRange}`),
    fetch('/api/statistics?type=heatmapStats')
  ]);

  const [overview, userGrowth, category, visit, activity, trend, heatmap] = 
    await Promise.all([
      overviewRes.json(),
      userGrowthRes.json(),
      categoryRes.json(),
      visitRes.json(),
      activityRes.json(),
      trendRes.json(),
      heatmapRes.json()
    ]);

  return {
    overview: overview.success ? overview.data : null,
    userGrowth: userGrowth.success ? userGrowth.data : [],
    category: category.success ? category.data : [],
    visit: visit.success ? visit.data : [],
    activity: activity.success ? activity.data : [],
    trend: trend.success ? trend.data : [],
    heatmap: heatmap.success ? heatmap.data : []
  };
}

// 预取数据函数
export function prefetchWebsites(page = 1, limit = 10, search = '', category = '') {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    search,
  });
  
  if (category) {
    params.append('category', category);
  }

  // 预取下一页数据
  if (typeof window !== 'undefined') {
    const nextParams = new URLSearchParams(params);
    nextParams.set('page', (page + 1).toString());
    
    fetch(`/api/links?${nextParams.toString()}`).catch(() => {
      // 静默处理预取错误
    });
  }
}

export default {
  useWebsites,
  useCategories,
  useCategoriesWithPagination,
  useUsers,
  useDashboardStats,
  prefetchWebsites
};

// SWR缓存清理工具
export const clearSWRCache = {
  // 清理所有links相关缓存
  links: () => {
    swrMutate((key) => typeof key === 'string' && key.startsWith('/api/links'), undefined, { revalidate: false });
    console.log('Cleared SWR links cache');
  },
  
  // 清理所有缓存
  all: () => {
    swrMutate(() => true, undefined, { revalidate: false });
    console.log('Cleared all SWR cache');
  }
};