'use client';

import { useState, useEffect } from 'react';
import { 
  AreaChart, Area, BarChart, Bar, LineChart, Line,
  PieChart, Pie, Cell, RadialBarChart, RadialBar,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer 
} from 'recharts';
import { formatDateShort } from '@/lib/utils/dateUtils';

interface ChartProps {
  data: any[];
  height?: number;
  className?: string;
}

// 现代化配色方案
const COLORS = {
  primary: '#3b82f6', // 蓝色
  success: '#10b981', // 绿色
  warning: '#f59e0b', // 橙色
  error: '#ef4444',   // 红色
  purple: '#8b5cf6',  // 紫色
  pink: '#ec4899',    // 粉色
  teal: '#14b8a6',    // 青色
  gray: '#6b7280'     // 灰色
};

const COLOR_PALETTE = [
  COLORS.primary, COLORS.success, COLORS.warning, 
  COLORS.error, COLORS.purple, COLORS.pink, COLORS.teal
];

// 安全的日期格式化函数
const formatDateSafeChart = (value: any) => {
  if (typeof window === 'undefined') return value;
  try {
    return formatDateShort(value);
  } catch {
    return value;
  }
};

// 自定义Tooltip样式
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
        <p className="text-sm font-medium text-foreground mb-2">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {entry.name}: {entry.value}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

// 1. 用户增长趋势图
export function UserGrowthChart({ data, height = 300, className = "" }: ChartProps) {
  return (
    <div className={`w-full ${className}`}>
      <ResponsiveContainer width="100%" height={height}>
        <AreaChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <defs>
            <linearGradient id="userGrowth" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={COLORS.primary} stopOpacity={0.3}/>
              <stop offset="95%" stopColor={COLORS.primary} stopOpacity={0}/>
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" stroke="#374151" opacity={0.3} />
          <XAxis 
            dataKey="date" 
            stroke="#9ca3af" 
            fontSize={12}
            tickFormatter={formatDateSafeChart}
          />
          <YAxis stroke="#9ca3af" fontSize={12} />
          <Tooltip content={<CustomTooltip />} />
          <Area 
            type="monotone" 
            dataKey="users" 
            stroke={COLORS.primary} 
            strokeWidth={3}
            fillOpacity={1} 
            fill="url(#userGrowth)" 
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
}

// 2. 分类统计饼图
export function CategoryPieChart({ data, height = 300, className = "" }: ChartProps) {
  return (
    <div className={`w-full ${className}`}>
      <ResponsiveContainer width="100%" height={height}>
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={false}
            outerRadius={"70%"}
            fill="#8884d8"
            dataKey="value"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLOR_PALETTE[index % COLOR_PALETTE.length]} />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
          <Legend wrapperStyle={{ fontSize: '12px' }} />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
}

// 3. 访问量柱状图
export function VisitBarChart({ data, height = 300, className = "" }: ChartProps) {
  return (
    <div className={`w-full ${className}`}>
      <ResponsiveContainer width="100%" height={height}>
        <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#374151" opacity={0.3} />
          <XAxis dataKey="name" stroke="#9ca3af" fontSize={12} />
          <YAxis stroke="#9ca3af" fontSize={12} />
          <Tooltip content={<CustomTooltip />} />
          <Bar dataKey="visits" fill={COLORS.success} radius={[4, 4, 0, 0]} />
          <Bar dataKey="clicks" fill={COLORS.warning} radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}

// 4. 实时活跃度仪表盘
export function ActivityGaugeChart({ data, height = 250, className = "" }: ChartProps) {
  const gaugeData = [
    { name: 'Active', value: data[0]?.value || 0, fill: COLORS.success },
    { name: 'Inactive', value: 100 - (data[0]?.value || 0), fill: '#e5e7eb' }
  ];

  return (
    <div className={`w-full ${className}`}>
      <ResponsiveContainer width="100%" height={height}>
        <RadialBarChart 
          cx="50%" 
          cy="50%" 
          innerRadius="50%" 
          outerRadius="80%" 
          data={gaugeData}
          startAngle={180}
          endAngle={0}
        >
          <RadialBar dataKey="value" cornerRadius={10} fill={COLORS.success} />
          <text 
            x="50%" 
            y="50%" 
            textAnchor="middle" 
            dominantBaseline="middle" 
            className="text-lg sm:text-2xl font-bold fill-foreground"
          >
            {data[0]?.value || 0}%
          </text>
        </RadialBarChart>
      </ResponsiveContainer>
    </div>
  );
}

// 5. 多维度折线图
export function MultiLineChart({ data, height = 300, className = "" }: ChartProps) {
  return (
    <div className={`w-full ${className}`}>
      <ResponsiveContainer width="100%" height={height}>
        <LineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#374151" opacity={0.3} />
          <XAxis 
            dataKey="date" 
            stroke="#9ca3af" 
            fontSize={12}
            tick={{ fontSize: 10 }}
          />
          <YAxis 
            stroke="#9ca3af" 
            fontSize={12}
            tick={{ fontSize: 10 }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend wrapperStyle={{ fontSize: '12px' }} />
          <Line 
            type="monotone" 
            dataKey="views" 
            stroke={COLORS.primary} 
            strokeWidth={2}
            dot={{ r: 3 }}
            activeDot={{ r: 5 }}
          />
          <Line 
            type="monotone" 
            dataKey="clicks" 
            stroke={COLORS.success} 
            strokeWidth={2}
            dot={{ r: 3 }}
            activeDot={{ r: 5 }}
          />
          <Line 
            type="monotone" 
            dataKey="shares" 
            stroke={COLORS.warning} 
            strokeWidth={2}
            dot={{ r: 3 }}
            activeDot={{ r: 5 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}

// 6. 热力图风格的活动图表
export function ActivityHeatmapChart({ data, height = 200, className = "" }: ChartProps) {
  return (
    <div className={`w-full ${className}`}>
      <ResponsiveContainer width="100%" height={height}>
        <BarChart 
          data={data} 
          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          layout="horizontal"
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#374151" opacity={0.3} />
          <XAxis 
            type="number" 
            stroke="#9ca3af" 
            fontSize={10}
            tick={{ fontSize: 10 }}
          />
          <YAxis 
            dataKey="hour" 
            type="category" 
            stroke="#9ca3af" 
            fontSize={10} 
            width={30}
            tick={{ fontSize: 9 }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar dataKey="activity" fill={COLORS.purple} radius={[0, 4, 4, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}