import { NextRequest, NextResponse } from 'next/server';
import { invalidateCache } from '@/lib/cache';

// 缓存清理API
export async function POST(request: NextRequest) {
  try {
    console.log('=== CACHE CLEAR API STARTED ===');
    
    // 清理所有缓存
    invalidateCache.all();
    
    return NextResponse.json({
      success: true,
      message: 'All cache cleared successfully',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('缓存清理错误:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}