/**
 * 安全的日期格式化工具函数
 * 确保服务端和客户端渲染一致性，避免hydration mismatch
 */

/**
 * 格式化日期为 YYYY-MM-DD 格式
 * @param dateString - 日期字符串
 * @returns 格式化后的日期字符串
 */
export function formatDateSafe(dateString: string): string {
  try {
    const date = new Date(dateString);
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '无效日期';
    }
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  } catch {
    return '日期错误';
  }
}

/**
 * 格式化日期为中文格式：YYYY年MM月DD日
 * @param dateString - 日期字符串
 * @returns 格式化后的中文日期字符串
 */
export function formatDateChinese(dateString: string): string {
  try {
    const date = new Date(dateString);
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '无效日期';
    }
    
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    
    return `${year}年${month}月${day}日`;
  } catch {
    return '日期错误';
  }
}

/**
 * 格式化日期为简短格式：MM-DD
 * @param dateString - 日期字符串
 * @returns 格式化后的简短日期字符串
 */
export function formatDateShort(dateString: string): string {
  try {
    const date = new Date(dateString);
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '无效';
    }
    
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${month}-${day}`;
  } catch {
    return '错误';
  }
}