import { NextRequest } from 'next/server';
import { query, queryOne } from '@/lib/database';
import { createApiResponse, createApiError, withRole } from '@/lib/utils';

// 仅超级管理员可管理角色
export const GET = withRole('super_admin')(async (request: NextRequest) => {
  try {
    const roles: any[] = await query(`
      SELECT
        r.id,
        r.name,
        COUNT(DISTINCT rp.permission_id) as permission_count,
        COUNT(DISTINCT u.id) as user_count
      FROM roles r
      LEFT JOIN role_permissions rp ON r.id = rp.role_id
      LEFT JOIN users u ON r.id = u.role_id
      GROUP BY r.id, r.name
      ORDER BY r.id
    `);

    if (roles.length === 0) {
      return createApiResponse(true, roles);
    }

    const ids = roles.map(r => r.id);
    const placeholders = ids.map(() => '?').join(',');
    const rp: any[] = await query(
      `SELECT rp.role_id, p.name FROM role_permissions rp JOIN permissions p ON p.id = rp.permission_id WHERE rp.role_id IN (${placeholders}) ORDER BY p.name`,
      ids
    );
    const map: Record<number, string[]> = {};
    rp.forEach(row => {
      (map[row.role_id] = map[row.role_id] || []).push(row.name);
    });

    const data = roles.map(r => ({ ...r, permissionNames: map[r.id] || [] }));
    return createApiResponse(true, data);
  } catch (error) {
    console.error('Get roles error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'Failed to fetch roles', error, 500);
  }
});

export const POST = withRole('super_admin')(async (request: NextRequest) => {
  try {
    const body = await request.json();
    const { name, permissionNames } = body as { name: string; permissionNames?: string[] };

    if (!name || !name.trim()) {
      return createApiError('VALIDATION_ERROR', 'Role name is required', null, 400);
    }

    const existing = await queryOne('SELECT id FROM roles WHERE name = ?', [name]);
    if (existing) {
      return createApiError('ROLE_EXISTS', 'Role already exists', null, 400);
    }

    const insertResult: any = await query('INSERT INTO roles (name) VALUES (?)', [name.trim()]);
    const roleId = insertResult.insertId;

    if (Array.isArray(permissionNames) && permissionNames.length > 0) {
      const placeholders = permissionNames.map(() => '?').join(',');
      const permissions = await query(`SELECT id, name FROM permissions WHERE name IN (${placeholders})`, permissionNames);
      if (permissions.length > 0) {
        const values: any[] = [];
        const tuples = permissions.map((p: any) => {
          values.push(roleId, p.id);
          return '(?, ?)';
        }).join(',');
        await query(`INSERT INTO role_permissions (role_id, permission_id) VALUES ${tuples}`, values);
      }
    }

    const created = await queryOne('SELECT id, name FROM roles WHERE id = ?', [roleId]);
    return createApiResponse(true, created, 'Role created');
  } catch (error) {
    console.error('Create role error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'Failed to create role', error, 500);
  }
});

