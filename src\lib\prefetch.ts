/**
 * 智能预加载和预取策略
 * 解决页面加载慢的问题，提前加载用户可能访问的数据
 */

import { useEffect, useCallback, useRef } from 'react';
import { mutate } from 'swr';

// 预取策略配置
const PREFETCH_CONFIG = {
  // 延迟时间（毫秒）
  delays: {
    immediate: 0,
    short: 100,
    medium: 500,
    long: 1000
  },
  // 优先级
  priorities: {
    critical: 1,
    high: 2,
    medium: 3,
    low: 4
  },
  // 缓存时间
  cacheTimes: {
    short: 30 * 1000,      // 30秒
    medium: 5 * 60 * 1000,  // 5分钟
    long: 30 * 60 * 1000    // 30分钟
  }
};

// 预取任务队列
class PrefetchQueue {
  private queue: Array<{
    key: string;
    fetcher: () => Promise<any>;
    priority: number;
    delay: number;
  }> = [];
  private processing = false;

  add(key: string, fetcher: () => Promise<any>, priority = PREFETCH_CONFIG.priorities.medium, delay = 0) {
    // 避免重复添加相同的预取任务
    if (this.queue.some(task => task.key === key)) {
      return;
    }

    this.queue.push({ key, fetcher, priority, delay });
    this.queue.sort((a, b) => a.priority - b.priority);
    
    if (!this.processing) {
      this.process();
    }
  }

  private async process() {
    this.processing = true;
    
    while (this.queue.length > 0) {
      const task = this.queue.shift();
      if (!task) continue;

      try {
        // 延迟执行
        if (task.delay > 0) {
          await new Promise(resolve => setTimeout(resolve, task.delay));
        }

        // 检查是否已经在缓存中
        const cached = await mutate(task.key);
        if (cached === undefined) {
          console.log(`Prefetching: ${task.key}`);
          await mutate(task.key, task.fetcher(), { revalidate: false });
        }
      } catch (error) {
        console.warn(`Prefetch failed for ${task.key}:`, error);
      }
    }
    
    this.processing = false;
  }
}

// 全局预取队列
const prefetchQueue = new PrefetchQueue();

/**
 * 预取API数据的通用函数
 */
export const prefetchData = (
  key: string, 
  fetcher: () => Promise<any>,
  priority?: number,
  delay?: number
) => {
  prefetchQueue.add(key, fetcher, priority, delay);
};

/**
 * 网站数据预取
 */
export const prefetchWebsites = (
  page = 1, 
  limit = 10, 
  search = '', 
  category = '',
  priority = PREFETCH_CONFIG.priorities.high
) => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    ...(search && { search }),
    ...(category && { category })
  });

  const key = `/api/links?${params.toString()}`;
  
  prefetchData(
    key,
    () => fetch(key).then(res => res.json()),
    priority,
    PREFETCH_CONFIG.delays.short
  );
};

/**
 * 分类数据预取
 */
export const prefetchCategories = (
  page = 1,
  limit = 10,
  priority = PREFETCH_CONFIG.priorities.medium
) => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString()
  });

  const key = `/api/categories?${params.toString()}`;
  
  prefetchData(
    key,
    () => fetch(key).then(res => res.json()),
    priority,
    PREFETCH_CONFIG.delays.medium
  );
};

/**
 * 统计数据预取
 */
export const prefetchStatistics = (
  type = 'overview',
  priority = PREFETCH_CONFIG.priorities.low
) => {
  const key = `/api/statistics?type=${type}`;
  
  prefetchData(
    key,
    () => fetch(key).then(res => res.json()),
    priority,
    PREFETCH_CONFIG.delays.long
  );
};

/**
 * 智能预取Hook - 基于用户行为预测
 */
export const useSmartPrefetch = () => {
  const prefetchTriggered = useRef(new Set<string>());

  // 页面预取策略
  const prefetchNextPage = useCallback((currentPage: number, totalPages: number, baseParams: any) => {
    const triggerKey = `nextPage-${currentPage}`;
    if (prefetchTriggered.current.has(triggerKey) || currentPage >= totalPages) {
      return;
    }

    prefetchTriggered.current.add(triggerKey);
    
    // 预取下一页
    prefetchWebsites(
      currentPage + 1,
      baseParams.limit || 10,
      baseParams.search || '',
      baseParams.category || '',
      PREFETCH_CONFIG.priorities.medium
    );
  }, []);

  // 分类预取策略
  const prefetchCategoryContent = useCallback((categoryId: string, delay = PREFETCH_CONFIG.delays.medium) => {
    const triggerKey = `category-${categoryId}`;
    if (prefetchTriggered.current.has(triggerKey)) {
      return;
    }

    prefetchTriggered.current.add(triggerKey);
    
    setTimeout(() => {
      prefetchWebsites(
        1, 10, '', categoryId,
        PREFETCH_CONFIG.priorities.high
      );
    }, delay);
  }, []);

  // 相关内容预取
  const prefetchRelatedContent = useCallback(() => {
    // 预取统计数据（仪表板可能用到）
    prefetchStatistics('overview', PREFETCH_CONFIG.priorities.low);
    
    // 预取分类列表（导航可能用到）
    prefetchCategories(1, 20, PREFETCH_CONFIG.priorities.medium);
  }, []);

  return {
    prefetchNextPage,
    prefetchCategoryContent,
    prefetchRelatedContent
  };
};

/**
 * 鼠标悬停预取Hook
 */
export const useHoverPrefetch = () => {
  const hoverTimeouts = useRef(new Map<string, NodeJS.Timeout>());

  const prefetchOnHover = useCallback((
    element: HTMLElement | null, 
    prefetchFn: () => void,
    delay = PREFETCH_CONFIG.delays.medium
  ) => {
    if (!element) return;

    const handleMouseEnter = () => {
      const timeout = setTimeout(prefetchFn, delay);
      hoverTimeouts.current.set(element.id || 'default', timeout);
    };

    const handleMouseLeave = () => {
      const timeout = hoverTimeouts.current.get(element.id || 'default');
      if (timeout) {
        clearTimeout(timeout);
        hoverTimeouts.current.delete(element.id || 'default');
      }
    };

    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      element.removeEventListener('mouseenter', handleMouseEnter);
      element.removeEventListener('mouseleave', handleMouseLeave);
      
      const timeout = hoverTimeouts.current.get(element.id || 'default');
      if (timeout) {
        clearTimeout(timeout);
        hoverTimeouts.current.delete(element.id || 'default');
      }
    };
  }, []);

  return { prefetchOnHover };
};

/**
 * 交集观察器预取Hook - 当元素即将进入视口时预取
 */
export const useIntersectionPrefetch = () => {
  const observerRef = useRef<IntersectionObserver | null>(null);
  const observedElements = useRef(new Set<Element>());

  useEffect(() => {
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const element = entry.target;
            const prefetchFn = (element as any).prefetchFn;
            
            if (prefetchFn && typeof prefetchFn === 'function') {
              prefetchFn();
              // 只预取一次
              observerRef.current?.unobserve(element);
              observedElements.current.delete(element);
            }
          }
        });
      },
      {
        rootMargin: '100px' // 提前100px开始预取
      }
    );

    return () => {
      observerRef.current?.disconnect();
    };
  }, []);

  const observeElement = useCallback((element: Element, prefetchFn: () => void) => {
    if (!observerRef.current || observedElements.current.has(element)) {
      return;
    }

    (element as any).prefetchFn = prefetchFn;
    observerRef.current.observe(element);
    observedElements.current.add(element);
  }, []);

  return { observeElement };
};

/**
 * 应用启动时的预热策略
 */
export const warmupApplication = () => {
  // 预取首页关键数据
  setTimeout(() => {
    prefetchWebsites(1, 20, '', '', PREFETCH_CONFIG.priorities.critical);
    prefetchCategories(1, 20, PREFETCH_CONFIG.priorities.high);
  }, PREFETCH_CONFIG.delays.immediate);

  // 预取统计数据
  setTimeout(() => {
    prefetchStatistics('overview', PREFETCH_CONFIG.priorities.medium);
  }, PREFETCH_CONFIG.delays.long);
};

/**
 * 基于用户行为的自适应预取
 */
export const useAdaptivePrefetch = () => {
  const behaviorStats = useRef({
    categoryClicks: new Map<string, number>(),
    searchQueries: new Map<string, number>(),
    pageViews: new Map<string, number>()
  });

  const recordBehavior = useCallback((type: 'category' | 'search' | 'page', value: string) => {
    const stats = behaviorStats.current;
    
    switch (type) {
      case 'category':
        stats.categoryClicks.set(value, (stats.categoryClicks.get(value) || 0) + 1);
        break;
      case 'search':
        stats.searchQueries.set(value, (stats.searchQueries.get(value) || 0) + 1);
        break;
      case 'page':
        stats.pageViews.set(value, (stats.pageViews.get(value) || 0) + 1);
        break;
    }

    // 基于行为预测预取
    adaptivePrefetch(type, value);
  }, []);

  const adaptivePrefetch = useCallback((type: 'category' | 'search' | 'page', value: string) => {
    const stats = behaviorStats.current;
    
    // 如果用户经常访问某个分类，预取相关内容
    if (type === 'category') {
      const clickCount = stats.categoryClicks.get(value) || 0;
      if (clickCount > 2) {
        prefetchWebsites(1, 20, '', value, PREFETCH_CONFIG.priorities.high);
      }
    }

    // 如果用户经常搜索某些关键词，预取相关结果
    if (type === 'search') {
      const searchCount = stats.searchQueries.get(value) || 0;
      if (searchCount > 1) {
        prefetchWebsites(1, 10, value, '', PREFETCH_CONFIG.priorities.medium);
      }
    }
  }, []);

  return { recordBehavior };
};

// 默认导出所有预取功能
export default {
  prefetchData,
  prefetchWebsites,
  prefetchCategories,
  prefetchStatistics,
  useSmartPrefetch,
  useHoverPrefetch,
  useIntersectionPrefetch,
  useAdaptivePrefetch,
  warmupApplication
};