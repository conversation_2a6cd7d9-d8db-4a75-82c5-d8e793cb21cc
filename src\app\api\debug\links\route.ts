import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/database';

// 调试API - 直接查询数据库
export async function GET(request: NextRequest) {
  try {
    console.log('=== DEBUG API STARTED ===');
    
    // 1. 测试基本数据库连接
    const testQuery = await query('SELECT 1 as test');
    console.log('数据库连接测试:', testQuery);
    
    // 2. 查询links表总数
    const countResult = await query('SELECT COUNT(*) as total FROM links');
    console.log('links表总记录数:', countResult);
    
    // 3. 查询前5条链接数据
    const linksResult = await query(`
      SELECT 
        l.id,
        l.title,
        l.url,
        l.description,
        l.category_id,
        c.name as category_name,
        l.user_id,
        u.username as created_by,
        l.is_private,
        l.created_at,
        l.updated_at
      FROM links l
      LEFT JOIN categories c ON l.category_id = c.id
      LEFT JOIN users u ON l.user_id = u.id
      LIMIT 5
    `);
    console.log('前5条链接数据:', linksResult);
    
    // 4. 检查权限过滤逻辑
    const publicLinks = await query('SELECT COUNT(*) as count FROM links WHERE is_private = false');
    const privateLinks = await query('SELECT COUNT(*) as count FROM links WHERE is_private = true');
    console.log('公开链接数量:', publicLinks);
    console.log('私有链接数量:', privateLinks);
    
    // 5. 模拟API正常查询逻辑
    const normalQueryResult = await query(`
      SELECT 
        l.id,
        l.title,
        l.url,
        l.description,
        l.category_id,
        c.name as category_name,
        l.user_id,
        u.username as created_by,
        l.is_private,
        l.icon_media_id,
        m.file_path as icon_url,
        l.created_at,
        l.updated_at
      FROM links l
      LEFT JOIN categories c ON l.category_id = c.id
      LEFT JOIN users u ON l.user_id = u.id
      LEFT JOIN media m ON l.icon_media_id = m.id
      WHERE 1=1
      ORDER BY l.created_at DESC
      LIMIT 10
    `);
    console.log('正常API查询结果:', normalQueryResult);
    
    // 返回调试信息
    return NextResponse.json({
      success: true,
      debug: {
        database_connection: testQuery,
        total_links: countResult[0],
        public_links: publicLinks[0],
        private_links: privateLinks[0],
        sample_links: linksResult,
        normal_query_result: normalQueryResult,
        sample_count: normalQueryResult.length
      }
    });
    
  } catch (error) {
    console.error('调试API错误:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      debug: 'Database query failed'
    }, { status: 500 });
  }
}