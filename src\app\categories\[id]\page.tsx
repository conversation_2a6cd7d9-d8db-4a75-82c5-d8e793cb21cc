import { query } from '@/lib/database';
import CategoryPageClient from './CategoryPageClient';

async function getCategories() {
  try {
    const categories = await query(`
      SELECT 
        id,
        name,
        \`order\`,
        is_private,
        (SELECT COUNT(*) FROM links WHERE category_id = categories.id) as links_count
      FROM categories
      WHERE is_private = false
      ORDER BY \`order\` ASC, name ASC
    `);
    
    return categories;
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

async function getLinksByCategory(categoryId: number) {
  try {
    const links = await query(`
      SELECT 
        l.id,
        l.title,
        l.url,
        l.description,
        l.category_id,
        c.name as category_name,
        l.is_private,
        l.icon_media_id,
        l.created_at,
        l.updated_at
      FROM links l
      LEFT JOIN categories c ON l.category_id = c.id
      WHERE l.category_id = ? AND l.is_private = false
      ORDER BY l.created_at DESC
    `, [categoryId]);
    
    return links;
  } catch (error) {
    console.error('Error fetching links by category:', error);
    return [];
  }
}

interface CategoryPageProps {
  params: {
    id: string;
  };
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const categories = await getCategories();
  const categoryId = parseInt(params.id);
  const links = await getLinksByCategory(categoryId);

  return (
    <CategoryPageClient
      initialCategories={categories}
      initialLinks={links}
      categoryId={categoryId}
    />
  );
}