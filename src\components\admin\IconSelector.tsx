'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import IconFetchProgress from '@/components/admin/IconFetchProgress';
import {
  Upload,
  Download,
  Palette,
  Loader2,
  Check,
  X,
  RefreshCw,
  Image as ImageIcon,
  Link as LinkIcon,
  Type
} from 'lucide-react';
import { useToast } from '@/contexts/ToastContext';

export interface IconSelection {
  type: 'upload' | 'fetch' | 'generate' | null;
  value: string | File | null;
  preview: string;
  mediaId?: number;
}

interface IconSelectorProps {
  initialIcon?: IconSelection;
  websiteUrl?: string;
  websiteTitle?: string;
  onIconSelect: (icon: IconSelection) => void;
  className?: string;
}

interface GeneratedIconParams {
  backgroundColor?: string;
  textColor?: string;
  size?: number;
}

const PREDEFINED_COLORS = [
  { bg: '#DBEAFE', text: '#3B82F6', name: '蓝色' },
  { bg: '#FEE2E2', text: '#EF4444', name: '红色' },
  { bg: '#D1FAE5', text: '#10B981', name: '绿色' },
  { bg: '#FEF3C7', text: '#F59E0B', name: '黄色' },
  { bg: '#EDE9FE', text: '#8B5CF6', name: '紫色' },
  { bg: '#CFFAFE', text: '#06B6D4', name: '青色' },
  { bg: '#FED7AA', text: '#F97316', name: '橙色' },
  { bg: '#FCE7F3', text: '#EC4899', name: '粉色' }
];

export default function IconSelector({
  initialIcon,
  websiteUrl,
  websiteTitle,
  onIconSelect,
  className
}: IconSelectorProps) {
  const [activeTab, setActiveTab] = useState<'upload' | 'fetch' | 'generate'>('upload');
  const [selectedIcon, setSelectedIcon] = useState<IconSelection>(
    initialIcon || { type: null, value: null, preview: '' }
  );
  const [loading, setLoading] = useState(false);
  const [uploadFile, setUploadFile] = useState<File | null>(null);
  const [fetchUrl, setFetchUrl] = useState(websiteUrl || '');
  const [generateParams, setGenerateParams] = useState<GeneratedIconParams>({
    backgroundColor: PREDEFINED_COLORS[0].bg,
    textColor: PREDEFINED_COLORS[0].text,
    size: 64
  });

  // 中断获取功能
  const [abortController, setAbortController] = useState<AbortController | null>(null);
  const [fetchingProgress, setFetchingProgress] = useState<string>('');

  // URL标准化（无协议时默认 https）
  const normalizeUrl = (input: string): string => {
    const val = (input || '').trim();
    if (!val) return '';
    const hasScheme = /^[a-zA-Z][a-zA-Z0-9+.-]*:\/\//.test(val);
    return hasScheme ? val : `https://${val}`;
  };

  // 覆盖背景毛玻璃进度弹窗状态（与列表页一致）
  const [progressModal, setProgressModal] = useState({
    isOpen: false,
    website: null as { id: number; title: string; url: string } | null,
    progress: {
      current: 0,
      total: 15,
      strategy: '准备中...',
      status: 'fetching' as 'fetching' | 'success' | 'error' | 'cancelled',
      message: '' as string
    }
  });

  const fileInputRef = useRef<HTMLInputElement>(null);
  const { showSuccess, showError } = useToast();

  useEffect(() => {
    if (websiteUrl) {
      setFetchUrl(websiteUrl);
    }
  }, [websiteUrl]);

  // 处理文件上传
  const handleFileUpload = async (file: File) => {
    if (!file) return;

    // 验证文件类型和大小
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
    if (!allowedTypes.includes(file.type)) {
      showError('文件类型错误', '请选择 JPG、PNG、GIF、WebP 或 SVG 格式的图片');
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      showError('文件过大', '图片文件大小不能超过 10MB');
      return;
    }

    setLoading(true);
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', 'icons');

    try {
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (result.success) {
        const iconSelection: IconSelection = {
          type: 'upload',
          value: file,
          preview: result.data.url,
          mediaId: result.data.id
        };

        setSelectedIcon(iconSelection);
        onIconSelect(iconSelection);
        showSuccess('上传成功', '图标已成功上传');
      } else {
        throw new Error(result.message || '上传失败');
      }
    } catch (error) {
      console.error('Upload error:', error);
      showError('上传失败', error instanceof Error ? error.message : '请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理favicon获取
  const handleFetchFavicon = async () => {
    if (!fetchUrl.trim()) {
      showError('URL错误', '请输入有效的网站URL');
      return;
    }

    const normalized = normalizeUrl(fetchUrl);

    // 创建新的AbortController
    const controller = new AbortController();
    setAbortController(controller);
    setLoading(true);
    setFetchingProgress('正在尝试多种方式获取网站图标...');

    try {
      // 打开进度弹窗
      setProgressModal({
        isOpen: true,
        website: { id: 0, title: websiteTitle || '未命名网站', url: normalized },
        progress: { current: 1, total: 15, strategy: '正在尝试多种策略获取图标...', status: 'fetching', message: '' }
      });

      const response = await fetch('/api/upload/fetch-favicon', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url: normalized, size: 64 }),
        signal: controller.signal // 传递中断信号
      });

      const result = await response.json();

      if (result.success) {
        const iconSelection: IconSelection = {
          type: 'fetch',
          value: fetchUrl,
          preview: result.data.url,
          mediaId: result.data.id
        };

        setSelectedIcon(iconSelection);
        onIconSelect(iconSelection);
        // 更新进度弹窗为成功
        setProgressModal(prev => ({
          ...prev,
          progress: {
            ...prev.progress,
            current: prev.progress.total,
            strategy: result.data.strategy || '获取完成',
            status: 'success',
            message: `已从 ${result.data.strategy} 获取到图标`
          }
        }));
        showSuccess('获取成功', `网站图标已成功获取 (策略: ${result.data.strategy})`);
      } else {
        // 更新进度弹窗为失败
        setProgressModal(prev => ({
          ...prev,
          progress: { ...prev.progress, status: 'error', message: result.message || '获取失败' }
        }));
        throw new Error(result.message || '获取失败');
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        setProgressModal(prev => ({ ...prev, progress: { ...prev.progress, status: 'cancelled', message: '用户取消操作' } }));
        showError('获取已取消', '图标获取已被用户取消');
      } else {
        console.error('Fetch error:', error);
        setProgressModal(prev => ({ ...prev, progress: { ...prev.progress, status: 'error', message: error instanceof Error ? error.message : '获取失败' } }));
        showError('获取失败', error instanceof Error ? error.message : '尝试了多种方式都无法获取该网站的图标');
      }
    } finally {
      setLoading(false);
      setFetchingProgress('');
      setAbortController(null);
    }
  };

  // 中断获取
  const handleAbortFetch = () => {
    if (abortController) {
      abortController.abort();
      setAbortController(null);
      setLoading(false);
      setFetchingProgress('');
    }
  };

  // 处理图标生成
  const handleGenerateIcon = async () => {
    if (!websiteTitle?.trim()) {
      showError('标题错误', '请先输入网站标题');
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('/api/upload/generate-icon', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: websiteTitle,
          size: generateParams.size,
          backgroundColor: generateParams.backgroundColor,
          textColor: generateParams.textColor
        })
      });

      const result = await response.json();

      if (result.success) {
        const iconSelection: IconSelection = {
          type: 'generate',
          value: websiteTitle,
          preview: result.data.dataUrl,
          mediaId: result.data.id
        };

        setSelectedIcon(iconSelection);
        onIconSelect(iconSelection);
        showSuccess('生成成功', '字母图标已成功生成');
      } else {
        throw new Error(result.message || '生成失败');
      }
    } catch (error) {
      console.error('Generate error:', error);
      showError('生成失败', error instanceof Error ? error.message : '请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setUploadFile(file);
      handleFileUpload(file);
    }
  };

  const clearSelection = () => {
    const emptyIcon: IconSelection = { type: null, value: null, preview: '' };
    setSelectedIcon(emptyIcon);
    onIconSelect(emptyIcon);
    setUploadFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 覆盖背景毛玻璃进度弹窗（放在根节点，独立于各个Tab） */}
      <IconFetchProgress
        isOpen={progressModal.isOpen}
        onClose={() => setProgressModal(prev => ({ ...prev, isOpen: false }))}
        onCancel={() => {
          if (abortController) abortController.abort();
          setProgressModal(prev => ({ ...prev, progress: { ...prev.progress, status: 'cancelled', message: '用户取消操作' } }));
        }}
        website={progressModal.website}
        progress={progressModal.progress}
      />
      {/* 选项卡 */}
      <div className="flex space-x-2 border-b">
        <button
          type="button"
          className={`pb-2 px-1 border-b-2 transition-colors ${
            activeTab === 'upload'
              ? 'border-primary text-primary'
              : 'border-transparent text-muted-foreground hover:text-foreground'
          }`}
          onClick={() => setActiveTab('upload')}
        >
          <div className="flex items-center space-x-2">
            <Upload className="w-4 h-4" />
            <span>上传图片</span>
          </div>
        </button>

        <button
          type="button"
          className={`pb-2 px-1 border-b-2 transition-colors ${
            activeTab === 'fetch'
              ? 'border-primary text-primary'
              : 'border-transparent text-muted-foreground hover:text-foreground'
          }`}
          onClick={() => setActiveTab('fetch')}
        >
          <div className="flex items-center space-x-2">
            <Download className="w-4 h-4" />
            <span>自动获取</span>
          </div>
        </button>

        <button
          type="button"
          className={`pb-2 px-1 border-b-2 transition-colors ${
            activeTab === 'generate'
              ? 'border-primary text-primary'
              : 'border-transparent text-muted-foreground hover:text-foreground'
          }`}
          onClick={() => setActiveTab('generate')}
        >
          <div className="flex items-center space-x-2">
            <Palette className="w-4 h-4" />
            <span>生成图标</span>
          </div>
        </button>
      </div>

      {/* 预览区域 */}
      {selectedIcon.preview && (
        <Card className="p-4 bg-muted/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex space-x-2">
                {/* 16px 预览 */}
                <div className="w-4 h-4 rounded border bg-white flex items-center justify-center overflow-hidden">
                  <img
                    src={selectedIcon.preview}
                    alt="Icon"
                    width={16}
                    height={16}
                    className="object-cover"
                    onError={(e) => {
                      (e.target as HTMLImageElement).style.display = 'none';
                    }}
                  />
                </div>

                {/* 32px 预览 */}
                <div className="w-8 h-8 rounded border bg-white flex items-center justify-center overflow-hidden">
                  <img
                    src={selectedIcon.preview}
                    alt="Icon"
                    width={32}
                    height={32}
                    className="object-cover"
                    onError={(e) => {
                      (e.target as HTMLImageElement).style.display = 'none';
                    }}
                  />
                </div>

                {/* 64px 预览 */}
                <div className="w-16 h-16 rounded border bg-white flex items-center justify-center overflow-hidden">
                  <img
                    src={selectedIcon.preview}
                    alt="Icon"
                    width={64}
                    height={64}
                    className="object-cover"
                    onError={(e) => {
                      (e.target as HTMLImageElement).style.display = 'none';
                    }}
                  />
                </div>
              </div>

              <div>
                <div className="flex items-center space-x-2">
                  <Badge variant="secondary">
                    {selectedIcon.type === 'upload' && '上传图片'}
                    {selectedIcon.type === 'fetch' && '自动获取'}
                    {selectedIcon.type === 'generate' && '生成图标'}
                  </Badge>
                  <Check className="w-4 h-4 text-green-500" />
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  图标预览 (16px, 32px, 64px)
                </p>
              </div>
            </div>

            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={clearSelection}
              className="text-muted-foreground hover:text-foreground"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </Card>
      )}

      {/* 内容区域 */}
      <div className="space-y-4">
        {activeTab === 'upload' && (
          <Card className="p-4">
            <CardContent className="p-0">
              <input
                ref={fileInputRef}
                type="file"
                accept="image/jpeg,image/png,image/gif,image/webp,image/svg+xml"
                onChange={handleFileInputChange}
                className="hidden"
              />

              <div
                className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center hover:border-primary/50 transition-colors cursor-pointer"
                onClick={() => fileInputRef.current?.click()}
              >
                <ImageIcon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <div className="space-y-2">
                  <p className="text-sm font-medium">点击上传图标文件</p>
                  <p className="text-xs text-muted-foreground">
                    支持 JPG、PNG、GIF、WebP、SVG 格式，最大 10MB
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {activeTab === 'fetch' && (
          <Card className="p-4">
            <CardContent className="p-0 space-y-4">
              <div>
                <label className="text-sm font-medium mb-2 block">网站URL</label>
                <div className="flex space-x-2">
                  <Input
                    placeholder="例如：https://example.com 或 example.com"
                    value={fetchUrl}
                    onChange={(e) => setFetchUrl(e.target.value)}
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setFetchUrl(prev => normalizeUrl(prev))}
                    disabled={loading || !fetchUrl.trim()}
                    className="px-3"
                    title="一键补全协议"
                  >
                    补全
                  </Button>
                  {loading && abortController ? (
                    <Button
                      type="button"
                      onClick={handleAbortFetch}
                      variant="outline"
                      className="px-4"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  ) : (
                    <Button
                      type="button"
                      onClick={handleFetchFavicon}
                      disabled={loading || !fetchUrl.trim()}
                      className="px-4"
                    >
                      {loading ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <RefreshCw className="w-4 h-4" />
                      )}
                    </Button>
                  )}
                </div>
              </div>

              <div className="bg-muted/50 p-3 rounded-lg">
                <div className="flex items-start space-x-2">
                  <LinkIcon className="w-4 h-4 text-muted-foreground mt-0.5" />
                  <div className="text-xs text-muted-foreground">
                    {fetchingProgress ? (
                      <>
                        <p className="font-medium mb-1 text-primary">获取进度：</p>
                        <p className="mb-2">{fetchingProgress}</p>
                        <p className="text-xs opacity-75">正在尝试15+种不同的获取策略，请稍候...</p>
                      </>
                    ) : (
                      <>
                        <p className="font-medium mb-1">自动获取说明：</p>
                        <ul className="list-disc list-inside space-y-1">
                          <li>系统会尝试从15+种不同源获取网站图标</li>
                          <li>包括 Google Favicons、DuckDuckGo、网站根目录等</li>
                          <li>支持 favicon.ico、logo.png、Apple Touch Icons等</li>
                          <li>获取的图标会自动保存到服务器，可随时中断</li>
                        </ul>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {activeTab === 'generate' && (
          <Card className="p-4">
            <CardContent className="p-0 space-y-4">
              <div>
                <label className="text-sm font-medium mb-2 block">网站标题</label>
                <Input
                  placeholder="网站名称"
                  value={websiteTitle || ''}
                  disabled
                  className="bg-muted"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  将使用标题的首字母生成图标
                </p>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">配色方案</label>
                <div className="grid grid-cols-4 gap-2">
                  {PREDEFINED_COLORS.map((color, index) => (
                    <button
                      type="button"
                      key={index}
                      className={`p-3 rounded-lg border-2 flex items-center justify-center transition-all ${
                        generateParams.backgroundColor === color.bg
                          ? 'border-primary shadow-md'
                          : 'border-muted hover:border-primary/50'
                      }`}
                      onClick={() => setGenerateParams({
                        ...generateParams,
                        backgroundColor: color.bg,
                        textColor: color.text
                      })}
                    >
                      <div
                        className="w-8 h-8 rounded flex items-center justify-center text-sm font-bold"
                        style={{
                          backgroundColor: color.bg,
                          color: color.text
                        }}
                      >
                        {websiteTitle?.charAt(0).toUpperCase() || 'A'}
                      </div>
                    </button>
                  ))}
                </div>

      {/* 覆盖背景毛玻璃进度弹窗 */}
      <IconFetchProgress
        isOpen={progressModal.isOpen}
        onClose={() => setProgressModal(prev => ({ ...prev, isOpen: false }))}
        onCancel={() => {
          if (abortController) abortController.abort();
          setProgressModal(prev => ({ ...prev, progress: { ...prev.progress, status: 'cancelled', message: '用户取消操作' } }));
        }}
        website={progressModal.website}
        progress={progressModal.progress}
      />

              </div>

              <Button
                type="button"
                onClick={handleGenerateIcon}
                disabled={loading || !websiteTitle}
                className="w-full"
              >
                {loading ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    生成中...
                  </>
                ) : (
                  <>
                    <Type className="w-4 h-4 mr-2" />
                    生成字母图标
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}