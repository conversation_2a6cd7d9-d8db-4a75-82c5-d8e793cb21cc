import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/database';
import { createApiResponse, createApiError } from '@/lib/utils';
import { generateCacheKey, withCache, CACHE_TTL } from '@/lib/cache';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'overview';
    const days = parseInt(searchParams.get('days') || '30');
    
    // 生成缓存键
    const cacheKey = generateCacheKey('statistics', { type, days });
    
    // 统计数据缓存时间较长，因为变化不频繁
    const cacheTTL = type === 'overview' ? CACHE_TTL.MEDIUM : CACHE_TTL.LONG;
    
    // 使用缓存包装器
    const fetchStatisticsData = withCache(
      cacheKey,
      cacheTTL,
      async () => {
        switch (type) {
          case 'overview':
            return await getOverviewStats();
          case 'userGrowth':
            return await getUserGrowthStats(days);
          case 'categoryStats':
            return await getCategoryStats();
          case 'visitStats':
            return await getVisitStats(days);
          case 'activityStats':
            return await getActivityStats();
          case 'trendStats':
            return await getTrendStats(days);
          case 'heatmapStats':
            return await getHeatmapStats();
          default:
            throw new Error('INVALID_TYPE');
        }
      }
    );
    
    try {
      const data = await fetchStatisticsData();
      return createApiResponse(true, data);
    } catch (error: any) {
      if (error.message === 'INVALID_TYPE') {
        return createApiError('INVALID_TYPE', 'Invalid statistics type', null, 400);
      }
      throw error;
    }
    
  } catch (error) {
    console.error('Get statistics error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'Failed to fetch statistics', error, 500);
  }
}

// 总览统计
async function getOverviewStats() {
  const [
    totalUsers,
    totalLinks,
    totalCategories,
    recentUsers,
    recentLinks,
    activeUsers
  ] = await Promise.all([
    query('SELECT COUNT(*) as count FROM users'),
    query('SELECT COUNT(*) as count FROM links'),
    query('SELECT COUNT(*) as count FROM categories'),
    query('SELECT COUNT(*) as count FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)'),
    query('SELECT COUNT(*) as count FROM links WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)'),
    query('SELECT COUNT(DISTINCT user_id) as count FROM activity_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)')
  ]);
  
  return {
    metrics: [
      {
        title: '总用户数',
        value: totalUsers[0].count,
        change: '+12%',
        trend: 'up',
        icon: 'users'
      },
      {
        title: '总链接数',
        value: totalLinks[0].count,
        change: '+8%',
        trend: 'up',
        icon: 'links'
      },
      {
        title: '分类数量',
        value: totalCategories[0].count,
        change: '+3%',
        trend: 'up',
        icon: 'categories'
      },
      {
        title: '活跃用户',
        value: activeUsers[0].count,
        change: '+15%',
        trend: 'up',
        icon: 'activity'
      }
    ],
    recent: {
      users: recentUsers[0].count,
      links: recentLinks[0].count
    }
  };
}

// 用户增长统计
async function getUserGrowthStats(days: number) {
  const userGrowthData = await query(`
    SELECT 
      DATE(created_at) as date,
      COUNT(*) as users
    FROM users 
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
    GROUP BY DATE(created_at)
    ORDER BY date ASC
  `, [days]);
  
  // 填充缺失的日期
  const filledData = [];
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  for (let i = 0; i < days; i++) {
    const currentDate = new Date(startDate);
    currentDate.setDate(startDate.getDate() + i);
    const dateStr = currentDate.toISOString().split('T')[0];
    
    const existingData = userGrowthData.find(item => 
      new Date(item.date).toISOString().split('T')[0] === dateStr
    );
    
    filledData.push({
      date: dateStr,
      users: existingData ? existingData.users : 0
    });
  }
  
  return filledData;
}

// 分类统计
async function getCategoryStats() {
  const categoryData = await query(`
    SELECT 
      c.name,
      COUNT(l.id) as value
    FROM categories c
    LEFT JOIN links l ON c.id = l.category_id
    GROUP BY c.id, c.name
    ORDER BY value DESC
  `);
  
  return categoryData;
}

// 访问统计
async function getVisitStats(days: number) {
  // 模拟访问数据 - 实际项目中应该从访问日志中获取
  const visitData = await query(`
    SELECT 
      c.name,
      COUNT(l.id) as visits,
      FLOOR(RAND() * 100) as clicks
    FROM categories c
    LEFT JOIN links l ON c.id = l.category_id
    GROUP BY c.id, c.name
    ORDER BY visits DESC
    LIMIT 10
  `);
  
  return visitData;
}

// 活跃度统计
async function getActivityStats() {
  const activeUsersCount = await query(`
    SELECT COUNT(DISTINCT user_id) as count 
    FROM activity_logs 
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
  `);
  
  const totalUsersCount = await query('SELECT COUNT(*) as count FROM users');
  
  const activityPercentage = totalUsersCount[0].count > 0 
    ? Math.round((activeUsersCount[0].count / totalUsersCount[0].count) * 100)
    : 0;
    
  return [{ name: 'Activity', value: activityPercentage }];
}

// 趋势统计
async function getTrendStats(days: number) {
  const trendData = await query(`
    SELECT 
      DATE(created_at) as date,
      COUNT(CASE WHEN action = 'link_view' THEN 1 END) as views,
      COUNT(CASE WHEN action = 'link_click' THEN 1 END) as clicks,
      COUNT(CASE WHEN action = 'link_share' THEN 1 END) as shares
    FROM activity_logs 
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
    GROUP BY DATE(created_at)
    ORDER BY date ASC
  `, [days]);
  
  // 填充缺失日期并模拟数据
  const filledData = [];
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  for (let i = 0; i < days; i++) {
    const currentDate = new Date(startDate);
    currentDate.setDate(startDate.getDate() + i);
    const dateStr = currentDate.toISOString().split('T')[0];
    
    const existingData = trendData.find(item => 
      new Date(item.date).toISOString().split('T')[0] === dateStr
    );
    
    filledData.push({
      date: dateStr,
      views: existingData ? existingData.views : Math.floor(Math.random() * 50),
      clicks: existingData ? existingData.clicks : Math.floor(Math.random() * 30),
      shares: existingData ? existingData.shares : Math.floor(Math.random() * 10)
    });
  }
  
  return filledData;
}

// 热力图统计
async function getHeatmapStats() {
  // 模拟24小时活跃度数据
  const heatmapData = [];
  for (let hour = 0; hour < 24; hour++) {
    heatmapData.push({
      hour: `${hour.toString().padStart(2, '0')}:00`,
      activity: Math.floor(Math.random() * 100)
    });
  }
  
  return heatmapData;
}