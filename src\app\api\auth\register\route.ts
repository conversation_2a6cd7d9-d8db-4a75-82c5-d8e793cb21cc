import { NextRequest, NextResponse } from 'next/server';
import { query, queryOne, insert } from '@/lib/database';
import { signJWT, createApiResponse, createApiError } from '@/lib/utils';
import { hashPassword } from '@/lib/utils/server';
import { registerSchema } from '@/lib/validation';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const validatedData = registerSchema.parse(body);
    const { username, email, password } = validatedData;
    
    const existingUser = await queryOne(
      'SELECT id FROM users WHERE username = ? OR email = ?',
      [username, email]
    );
    
    if (existingUser) {
      return createApiError('USER_ALREADY_EXISTS', 'Username or email already exists', null, 400);
    }
    
    const defaultRole = await queryOne('SELECT id FROM roles WHERE name = ?', ['user']);
    if (!defaultRole) {
      return createApiError('INTERNAL_SERVER_ERROR', 'Default role not found', null, 500);
    }
    
    const hashedPassword = await hashPassword(password);
    
    const userId = await insert(
      'INSERT INTO users (username, email, password, role_id, created_at, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW())',
      [username, email, hashedPassword, defaultRole.id]
    );
    
    const newUser = await queryOne(
      `SELECT 
        u.id,
        u.username,
        u.email,
        u.role_id,
        r.name as role,
        u.created_at,
        u.updated_at
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.id
      WHERE u.id = ?`,
      [userId]
    );
    
    const permissions = await query(
      `SELECT p.name
       FROM permissions p
       JOIN role_permissions rp ON p.id = rp.permission_id
       WHERE rp.role_id = ?`,
      [defaultRole.id]
    );
    
    const token = await signJWT({
      id: newUser.id,
      username: newUser.username,
      email: newUser.email,
      role: newUser.role,
      permissions: permissions.map(p => p.name)
    });
    
    await query(
      'INSERT INTO activity_logs (user_id, action, target_type, target_id, created_at) VALUES (?, ?, ?, ?, NOW())',
      [userId, '用户注册', 'user', userId]
    );
    
    const responseData = {
      user: {
        id: newUser.id,
        username: newUser.username,
        email: newUser.email,
        role: newUser.role,
        createdAt: newUser.created_at,
        updatedAt: newUser.updated_at
      },
      token,
      permissions: permissions.map(p => p.name)
    };
    
    const response = createApiResponse(true, responseData, 'Registration successful', 201);
    
    response.cookies.set('token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60,
      path: '/'
    });
    
    return response;
    
  } catch (error) {
    console.error('Registration error:', error);
    
    if (error instanceof Error && error.message.includes('Validation error')) {
      return createApiError('VALIDATION_ERROR', error.message, null, 400);
    }
    
    return createApiError('INTERNAL_SERVER_ERROR', 'Registration failed', error, 500);
  }
}