import { NextRequest, NextResponse } from 'next/server';
import { query, queryOne } from '@/lib/database';
import { createApiResponse, createApiError, withAuth, withPermission } from '@/lib/utils';
import { hashPassword } from '@/lib/utils/server';

export const GET = withAuth(async (request: NextRequest & { user: any }) => {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const role = searchParams.get('role') || '';
    const sortBy = searchParams.get('sortBy') || 'created_at';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const offset = (page - 1) * limit;

    let whereClause = 'WHERE 1=1';

    // 权限隔离：非超级管理员隐藏超级管理员账号
    let currentUser: any = null;
    try { currentUser = (request as any).user; } catch {}
    const isSuperAdmin = currentUser?.role === 'super_admin';
    if (!isSuperAdmin) {
      whereClause += " AND r.name <> 'super_admin'";
    }
    const params: any[] = [];

    if (search) {
      whereClause += ' AND (u.username LIKE ? OR u.email LIKE ?)';
      params.push(`%${search}%`, `%${search}%`);
    }

    if (role) {
      whereClause += ' AND r.name = ?';
      params.push(role);
    }

    const orderByClause = `ORDER BY u.${sortBy} ${sortOrder.toUpperCase()}`;

    const usersQuery = `
      SELECT
        u.id,
        u.username,
        u.email,
        u.role_id,
        r.name as role,
        u.avatar_media_id,
        u.created_at,
        u.updated_at,
        (SELECT COUNT(*) FROM activity_logs WHERE user_id = u.id) as action_count
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.id
      ${whereClause}
      ${orderByClause}
      LIMIT ? OFFSET ?
    `;

    const countQuery = `
      SELECT COUNT(*) as total
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.id
      ${whereClause}
    `;

    const [users, countResult] = await Promise.all([
      query(usersQuery, [...params, limit, offset]),
      queryOne(countQuery, params)
    ]);

    const total = countResult.total;
    const totalPages = Math.ceil(total / limit);

    return createApiResponse(true, {
      users,
      pagination: {
        page,
        limit,
        total,
        totalPages
      }
    });

  } catch (error) {
    console.error('Get users error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'Failed to fetch users', error, 500);
  }
});

export const POST = withPermission('users:create')(async (request: NextRequest) => {
  try {
    const body = await request.json();

    const { username, email, password, role, avatar } = body;

    if (!username || !email || !password || !role) {
      return createApiError('VALIDATION_ERROR', 'Missing required fields', null, 400);
    }

    const existingUser = await queryOne(
      'SELECT id FROM users WHERE username = ? OR email = ?',
      [username, email]
    );

    if (existingUser) {
      return createApiError('USER_ALREADY_EXISTS', 'Username or email already exists', null, 400);
    }

    const roleQuery = await queryOne('SELECT id FROM roles WHERE name = ?', [role]);
    if (!roleQuery) {
      return createApiError('INVALID_ROLE', 'Invalid role', null, 400);
    }

    const hashedPassword = await hashPassword(password);

    const result = await query(
      'INSERT INTO users (username, email, password, role_id, avatar_media_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())',
      [username, email, hashedPassword, roleQuery.id, avatar || null]
    );

    const newUser = await queryOne(
      'SELECT id, username, email, role_id, avatar_media_id, created_at, updated_at FROM users WHERE id = ?',
      [(result as any).insertId]
    );

    return createApiResponse(true, newUser, 'User created successfully', 201);
  } catch (error) {
    console.error('Create user error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'Failed to create user', error, 500);
  }
});