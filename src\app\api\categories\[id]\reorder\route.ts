import { NextRequest } from 'next/server';
import { query, queryOne, transaction } from '@/lib/database';
import { createApiResponse, createApiError, withAuth } from '@/lib/utils';
import { invalidateCache } from '@/lib/cache';

// 拖拽重新排序分类
export const POST = withAuth(async (request: NextRequest & { user: any }, { params }: { params: { id: string } }) => {
  try {
    const resolvedParams = await params;
    const draggedCategoryId = parseInt(resolvedParams.id);
    const body = await request.json();
    const { targetId } = body;

    if (!targetId) {
      return createApiError('VALIDATION_ERROR', '目标分类ID必填', null, 400);
    }

    const targetCategoryId = parseInt(targetId);

    // 获取被拖拽分类和目标分类的信息
    const [draggedCategory, targetCategory] = await Promise.all([
      queryOne('SELECT id, `order` FROM categories WHERE id = ?', [draggedCategoryId]),
      queryOne('SELECT id, `order` FROM categories WHERE id = ?', [targetCategoryId])
    ]);

    if (!draggedCategory || !targetCategory) {
      return createApiError('CATEGORY_NOT_FOUND', '分类不存在', null, 404);
    }

    if (draggedCategory.id === targetCategory.id) {
      return createApiResponse(true, { message: '无需变更' });
    }

    // 获取所有分类按order排序
    const allCategories = await query(
      'SELECT id, `order` FROM categories ORDER BY `order` ASC, id ASC'
    );

    // 找到拖拽分类和目标分类在数组中的位置
    const draggedIndex = allCategories.findIndex((cat: any) => cat.id === draggedCategoryId);
    const targetIndex = allCategories.findIndex((cat: any) => cat.id === targetCategoryId);

    // 重新计算order值
    const newOrder = [...allCategories];
    const draggedItem = newOrder.splice(draggedIndex, 1)[0];
    newOrder.splice(targetIndex, 0, draggedItem);

    // 使用连接级事务批量更新，避免部分更新导致顺序紊乱
    await transaction(async (conn) => {
      for (let i = 0; i < newOrder.length; i++) {
        const newOrderValue = (i + 1) * 10; // 用10的倍数，便于后续插入
        if (newOrder[i].order !== newOrderValue) {
          await conn.execute(
            'UPDATE categories SET `order` = ? WHERE id = ?',
            [newOrderValue, newOrder[i].id]
          );
        }
      }
      // 记录操作日志
      await conn.execute(
        'INSERT INTO activity_logs (user_id, action, target_type, target_id, created_at) VALUES (?, ?, ?, ?, NOW())',
        [request.user.id, 'category_reorder', 'category', draggedCategoryId]
      );
      return null;
    });

    // 排序完成后失效分类相关缓存，确保前端拿到最新顺序
    invalidateCache.categories();

    return createApiResponse(true, { 
      draggedId: draggedCategoryId, 
      targetId: targetCategoryId 
    }, '分类排序更新成功');
    
  } catch (error) {
    console.error('Reorder categories error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', '重新排序失败', error, 500);
  }
});