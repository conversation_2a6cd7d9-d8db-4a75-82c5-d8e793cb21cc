/*
 性能优化索引 - 解决页面加载慢的问题
 执行时间：2025-08-06
 目标：优化网站查询性能，减少页面加载时间
*/

USE navigation_db;

-- 1. 优化links表的主要查询场景
-- 添加复合索引用于分类页面查询（category_id + is_private + created_at）
ALTER TABLE `links` ADD INDEX `idx_category_private_created` (`category_id`, `is_private`, `created_at` DESC);

-- 添加复合索引用于公开网站按时间排序查询
ALTER TABLE `links` ADD INDEX `idx_private_created` (`is_private`, `created_at` DESC);

-- 添加标题索引用于搜索功能优化
ALTER TABLE `links` ADD INDEX `idx_title` (`title`);

-- 添加URL索引用于搜索和去重
ALTER TABLE `links` ADD INDEX `idx_url` (`url`(255));

-- 添加描述全文索引用于搜索（如果支持全文搜索）
-- 注意：MySQL 5.7+支持InnoDB全文索引
ALTER TABLE `links` ADD FULLTEXT INDEX `ft_description` (`description`);

-- 添加用户私有网站查询索引
ALTER TABLE `links` ADD INDEX `idx_user_private_created` (`user_id`, `is_private`, `created_at` DESC);

-- 2. 优化categories表查询
-- 添加复合索引用于按可见性和排序查询分类
ALTER TABLE `categories` ADD INDEX `idx_private_order` (`is_private`, `order`);

-- 添加用户分类查询索引
ALTER TABLE `categories` ADD INDEX `idx_user_private_order` (`user_id`, `is_private`, `order`);

-- 3. 优化activity_logs表查询
-- 添加操作目标查询索引
ALTER TABLE `activity_logs` ADD INDEX `idx_target_created` (`target_type`, `target_id`, `created_at` DESC);

-- 添加用户操作时间查询索引
ALTER TABLE `activity_logs` ADD INDEX `idx_user_created` (`user_id`, `created_at` DESC);

-- 4. 优化sessions表
-- 添加用户session查询索引
ALTER TABLE `sessions` ADD INDEX `idx_user_expires` (`user_id`, `expires_at`);

-- 5. 优化users表查询
-- 添加角色查询索引
ALTER TABLE `users` ADD INDEX `idx_role_created` (`role_id`, `created_at` DESC);

-- 6. 优化role_permissions表查询
-- 已有主键索引和外键索引，无需额外添加

-- 7. 查看索引使用情况的查询（用于后续监控）
/*
-- 查看表的索引使用情况
SELECT 
    TABLE_SCHEMA,
    TABLE_NAME,
    INDEX_NAME,
    CARDINALITY,
    SUB_PART,
    NULLABLE,
    INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'navigation_db' 
    AND TABLE_NAME IN ('links', 'categories', 'activity_logs', 'sessions', 'users')
ORDER BY TABLE_NAME, SEQ_IN_INDEX;

-- 查看慢查询（需要开启慢查询日志）
-- SET GLOBAL slow_query_log = 'ON';
-- SET GLOBAL long_query_time = 2;
*/

-- 8. 说明文档
/*
索引优化说明：

1. idx_category_private_created: 
   - 优化按分类和可见性查询网站，按创建时间排序
   - 适用于首页和分类页面的网站列表查询

2. idx_private_created:
   - 优化按可见性查询所有网站，按创建时间排序
   - 适用于首页显示公开网站的查询

3. idx_title, idx_url:
   - 优化搜索功能，支持按标题和URL快速查询
   
4. ft_description:
   - 全文索引优化描述字段的搜索功能
   
5. idx_user_private_created:
   - 优化用户个人网站查询，按创建时间排序

6. idx_private_order:
   - 优化分类显示，按可见性和排序字段查询

7. idx_target_created:
   - 优化操作日志查询，按操作对象和时间排序

预期性能提升：
- 首页加载速度提升 60-80%
- 分类页面查询速度提升 70%
- 搜索功能响应时间减少 50%
- 管理后台列表页面加载提升 40%
*/