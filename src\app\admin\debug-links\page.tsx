'use client';

import { useEffect, useState } from 'react';
import { useWebsites, clearSWRCache } from '@/hooks/useApiData';

export default function DebugLinksPage() {
  const [apiTestResult, setApiTestResult] = useState<any>(null);
  
  // 使用相同的hook来测试数据获取
  const { websites: websitesData, isLoading, error, mutate } = useWebsites(1, 10, '', '');
  
  // 手动测试API调用
  const testApiDirect = async () => {
    try {
      console.log('Testing direct API call...');
      const response = await fetch('/api/links?page=1&limit=5', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      const data = await response.json();
      console.log('Direct API response:', data);
      
      setApiTestResult({
        status: response.status,
        ok: response.ok,
        data: data
      });
    } catch (error) {
      console.error('Direct API test failed:', error);
      setApiTestResult({
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };
  
  // 清理缓存测试
  const clearCache = async () => {
    try {
      // 清理服务端缓存
      const response = await fetch('/api/debug/clear-cache', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      const result = await response.json();
      console.log('Server cache clear result:', result);
      
      // 清理SWR缓存
      clearSWRCache.all();
      
      // 重新获取数据
      mutate();
    } catch (error) {
      console.error('Cache clear failed:', error);
    }
  };
  
  // 清理SWR缓存并重新加载
  const clearSWROnly = async () => {
    console.log('=== 强制清除SWR缓存 ===');
    
    // 清除所有links相关的SWR缓存
    clearSWRCache.links();
    
    // 等待一会再重新获取
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // 强制重新获取数据
    await mutate();
    
    console.log('SWR缓存已清除并重新获取数据');
  };
  
  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">网站管理数据流调试</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 基本信息 */}
        <div className="bg-white rounded-lg shadow p-4">
          <h2 className="text-lg font-semibold mb-3">Hook状态</h2>
          <div className="space-y-2 text-sm">
            <p><strong>加载状态:</strong> {isLoading ? '加载中...' : '已完成'}</p>
            <p><strong>错误信息:</strong> {error ? error.message : '无错误'}</p>
            <p><strong>数据存在:</strong> {websitesData ? '是' : '否'}</p>
          </div>
        </div>
        
        {/* Hook结构分析 */}
        <div className="bg-white rounded-lg shadow p-4">
          <h2 className="text-lg font-semibold mb-3">Hook结构深度分析</h2>
          <div className="space-y-2 text-sm">
            <p><strong>websitesData类型:</strong> {typeof websitesData}</p>
            <p><strong>websitesData.websites类型:</strong> {typeof websitesData.websites}</p>
            <p><strong>websitesData.websites是对象:</strong> {websitesData.websites && typeof websitesData.websites === 'object' ? '是' : '否'}</p>
            <p><strong>websitesData.websites是数组:</strong> {Array.isArray(websitesData.websites) ? '是 (错误!)' : '否 (正确)'}</p>
            <p><strong>websites.websites是数组:</strong> {websitesData.websites?.websites && Array.isArray(websitesData.websites.websites) ? '是 (正确)' : '否 (错误)'}</p>
            <p><strong>websites.websites长度:</strong> {websitesData.websites?.websites?.length || 'N/A'}</p>
            <p><strong>websites.total:</strong> {websitesData.websites?.total || 'N/A'}</p>
            <p><strong>websites.page:</strong> {websitesData.websites?.page || 'N/A'}</p>
            <p><strong>websites.totalPages:</strong> {websitesData.websites?.totalPages || 'N/A'}</p>
          </div>
        </div>
        
        {/* 操作按钮 */}
        <div className="bg-white rounded-lg shadow p-4">
          <h2 className="text-lg font-semibold mb-3">调试操作</h2>
          <div className="space-y-2">
            <button 
              onClick={testApiDirect}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 mr-2"
            >
              直接测试API
            </button>
            <button 
              onClick={clearCache}
              className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 mr-2"
            >
              清理所有缓存
            </button>
            <button 
              onClick={clearSWROnly}
              className="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 mr-2"
            >
              🔄 强制清除SWR缓存
            </button>
            <button 
              onClick={() => mutate()}
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
            >
              重新加载数据
            </button>
          </div>
        </div>
        
        {/* API测试结果 */}
        <div className="bg-white rounded-lg shadow p-4">
          <h2 className="text-lg font-semibold mb-3">API测试结果</h2>
          {apiTestResult ? (
            <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-32">
              {JSON.stringify(apiTestResult, null, 2)}
            </pre>
          ) : (
            <p className="text-gray-500 text-sm">点击"直接测试API"按钮查看结果</p>
          )}
        </div>
      </div>
      
      {/* Hook数据详情 */}
      <div className="bg-white rounded-lg shadow p-4 mt-6">
        <h2 className="text-lg font-semibold mb-3">Hook数据详情</h2>
        {websitesData ? (
          <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto max-h-96">
            {JSON.stringify(websitesData, null, 2)}
          </pre>
        ) : (
          <p className="text-gray-500">暂无数据</p>
        )}
      </div>
      
      {/* 网站列表预览 */}
      {websitesData?.websites?.websites && websitesData.websites.websites.length > 0 && (
        <div className="bg-white rounded-lg shadow p-4 mt-6">
          <h2 className="text-lg font-semibold mb-3">网站列表预览</h2>
          <div className="space-y-2">
            {websitesData.websites.websites.slice(0, 3).map((website: any, index: number) => (
              <div key={index} className="border p-2 rounded text-sm">
                <p><strong>ID:</strong> {website.id}</p>
                <p><strong>标题:</strong> {website.title}</p>
                <p><strong>URL:</strong> {website.url}</p>
                <p><strong>私有:</strong> {website.is_private ? '是' : '否'}</p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}