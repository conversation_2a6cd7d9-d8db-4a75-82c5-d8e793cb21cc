'use client';

import { FixedSizeList as List } from 'react-window';
import { forwardRef, useMemo } from 'react';
import LinkCard from '@/components/features/LinkCard';

interface VirtualListProps {
  items: any[];
  height: number;
  itemHeight: number;
  width?: string | number;
  className?: string;
  renderItem: (props: { index: number; style: any; data: any }) => React.ReactNode;
}

// 虚拟化列表项组件
const ListItem = ({ index, style, data }: { index: number; style: any; data: any }) => (
  <div style={style}>
    {data.renderItem({ index, item: data.items[index] })}
  </div>
);

// 网站卡片虚拟列表
export const VirtualWebsiteList = forwardRef<
  List,
  {
    websites: any[];
    height: number;
    itemHeight?: number;
    className?: string;
  }
>(({ websites, height, itemHeight = 280, className }, ref) => {
  const itemData = useMemo(() => ({
    items: websites,
    renderItem: ({ item }: { item: any }) => (
      <div className="p-2">
        <LinkCard link={item} />
      </div>
    )
  }), [websites]);

  if (websites.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">暂无网站数据</p>
      </div>
    );
  }

  return (
    <div className={className}>
      <List
        ref={ref}
        height={height}
        itemCount={websites.length}
        itemSize={itemHeight}
        itemData={itemData}
        overscanCount={5}
        width="100%"
      >
        {ListItem}
      </List>
    </div>
  );
});

VirtualWebsiteList.displayName = 'VirtualWebsiteList';

// 管理页面表格行虚拟列表
export const VirtualTableList = forwardRef<
  List,
  {
    items: any[];
    height: number;
    itemHeight?: number;
    className?: string;
    renderItem: (props: { index: number; item: any }) => React.ReactNode;
  }
>(({ items, height, itemHeight = 80, className, renderItem }, ref) => {
  const itemData = useMemo(() => ({
    items,
    renderItem
  }), [items, renderItem]);

  if (items.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">暂无数据</p>
      </div>
    );
  }

  return (
    <div className={className}>
      <List
        ref={ref}
        height={height}
        itemCount={items.length}
        itemSize={itemHeight}
        itemData={itemData}
        overscanCount={10}
        width="100%"
      >
        {ListItem}
      </List>
    </div>
  );
});

VirtualTableList.displayName = 'VirtualTableList';

// 通用虚拟列表组件
const VirtualList = forwardRef<List, VirtualListProps>(
  ({ items, height, itemHeight, width = '100%', className, renderItem }, ref) => {
    const itemData = useMemo(() => ({
      items,
      renderItem: ({ index, item }: { index: number; item: any }) => 
        renderItem({ index, style: {}, data: item })
    }), [items, renderItem]);

    return (
      <div className={className}>
        <List
          ref={ref}
          height={height}
          itemCount={items.length}
          itemSize={itemHeight}
          itemData={itemData}
          width={width}
          overscanCount={5}
        >
          {ListItem}
        </List>
      </div>
    );
  }
);

VirtualList.displayName = 'VirtualList';

export default VirtualList;