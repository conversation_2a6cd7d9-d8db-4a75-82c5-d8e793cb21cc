/*
 Navicat Premium Data Transfer

 Source Server         : 1
 Source Server Type    : MySQL
 Source Server Version : 50714 (5.7.14-log)
 Source Host           : localhost:3306
 Source Schema         : navigation_db

 Target Server Type    : MySQL
 Target Server Version : 50714 (5.7.14-log)
 File Encoding         : 65001

 Date: 03/08/2025 17:31:18
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for activity_logs
-- ----------------------------
DROP TABLE IF EXISTS `activity_logs`;
CREATE TABLE `activity_logs`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作描述',
  `target_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作对象类型 (e.g., user, link)',
  `target_id` int(11) NULL DEFAULT NULL COMMENT '操作对象ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  CONSTRAINT `activity_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户操作日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of activity_logs
-- ----------------------------

-- ----------------------------
-- Table structure for categories
-- ----------------------------
DROP TABLE IF EXISTS `categories`;
CREATE TABLE `categories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `order` int(11) NULL DEFAULT 0 COMMENT '排序字段',
  `icon_media_id` int(11) NULL DEFAULT NULL COMMENT '分类图标媒体文件ID',
  `user_id` int(11) NULL DEFAULT NULL,
  `is_private` tinyint(1) NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_icon_media_id`(`icon_media_id`) USING BTREE,
  INDEX `idx_category_user_id`(`user_id`) USING BTREE,
  INDEX `idx_category_is_private`(`is_private`) USING BTREE,
  CONSTRAINT `categories_ibfk_1` FOREIGN KEY (`icon_media_id`) REFERENCES `media` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `fk_categories_icon_media` FOREIGN KEY (`icon_media_id`) REFERENCES `media` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '导航分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of categories
-- ----------------------------
INSERT INTO `categories` VALUES (1, '常用工具', 4, NULL, NULL, 0, '2025-08-03 05:14:24', '2025-08-03 05:30:02');
INSERT INTO `categories` VALUES (2, '开发者社区', 3, NULL, NULL, 0, '2025-08-03 05:14:24', '2025-08-03 05:29:58');
INSERT INTO `categories` VALUES (3, '设计资源', 3, NULL, NULL, 0, '2025-08-03 05:14:24', '2025-08-03 05:30:05');
INSERT INTO `categories` VALUES (4, '学习资源', 0, NULL, NULL, 0, '2025-08-03 05:14:24', '2025-08-03 05:51:10');
INSERT INTO `categories` VALUES (5, '开发工具', 1, NULL, NULL, 0, '2025-08-03 05:14:24', '2025-08-03 05:51:10');
INSERT INTO `categories` VALUES (6, '设计资源', 1, NULL, NULL, 0, '2025-08-03 05:14:24', '2025-08-03 05:30:24');
INSERT INTO `categories` VALUES (7, '学习平台', -1, NULL, NULL, 0, '2025-08-03 05:14:24', '2025-08-03 05:50:55');
INSERT INTO `categories` VALUES (8, '生产力工具', -3, NULL, NULL, 0, '2025-08-03 05:14:24', '2025-08-03 05:51:19');
INSERT INTO `categories` VALUES (9, '社交媒体', 5, NULL, NULL, 0, '2025-08-03 05:14:24', '2025-08-03 05:14:24');
INSERT INTO `categories` VALUES (10, '在线服务', 6, NULL, NULL, 0, '2025-08-03 05:14:24', '2025-08-03 05:14:24');
INSERT INTO `categories` VALUES (11, '测试分类', -2, NULL, NULL, 0, '2025-08-03 05:14:24', '2025-08-03 05:51:00');

-- ----------------------------
-- Table structure for links
-- ----------------------------
DROP TABLE IF EXISTS `links`;
CREATE TABLE `links`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '网站标题',
  `url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '网站URL',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '网站描述',
  `category_id` int(11) NOT NULL COMMENT '所属分类ID',
  `user_id` int(11) NULL DEFAULT NULL COMMENT '创建者ID, 公开链接可为NULL',
  `is_private` tinyint(1) NULL DEFAULT 0 COMMENT '是否为私密链接',
  `icon_media_id` int(11) NULL DEFAULT NULL COMMENT '链接图标媒体文件ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_icon_media_id`(`icon_media_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_is_private`(`is_private`) USING BTREE,
  CONSTRAINT `links_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `links_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `links_ibfk_3` FOREIGN KEY (`icon_media_id`) REFERENCES `media` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `fk_links_icon_media` FOREIGN KEY (`icon_media_id`) REFERENCES `media` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 27 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '导航链接表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of links
-- ----------------------------
INSERT INTO `links` VALUES (2, 'GitHub', 'https://github.com', '全球最大的代码托管平台', 5, 1, 0, NULL, '2025-08-02 21:34:11', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (3, 'Stack Overflow', 'https://stackoverflow.com', '程序员问答社区', 5, 1, 0, NULL, '2025-08-02 21:34:11', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (4, 'Visual Studio Code', 'https://code.visualstudio.com', '微软开发的免费代码编辑器', 5, 1, 0, NULL, '2025-08-02 21:34:11', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (5, 'NPM', 'https://www.npmjs.com', 'Node.js包管理器', 5, 1, 0, NULL, '2025-08-02 21:34:11', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (6, 'Figma', 'https://figma.com', '在线协作设计工具', 6, 1, 0, NULL, '2025-08-02 21:34:11', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (7, 'Unsplash', 'https://unsplash.com', '免费高质量图片素材', 6, 1, 0, NULL, '2025-08-02 21:34:11', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (8, 'Dribbble', 'https://dribbble.com', '设计师作品展示平台', 6, 1, 0, NULL, '2025-08-02 21:34:11', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (9, 'Adobe Color', 'https://color.adobe.com', '在线配色工具', 6, 1, 0, NULL, '2025-08-02 21:34:11', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (10, 'MDN Web Docs', 'https://developer.mozilla.org', 'Web开发权威文档', 7, 1, 0, NULL, '2025-08-02 21:34:11', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (11, 'Coursera', 'https://coursera.org', '在线大学课程平台', 7, 1, 0, NULL, '2025-08-02 21:34:11', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (12, 'freeCodeCamp', 'https://freecodecamp.org', '免费编程学习平台', 7, 1, 0, NULL, '2025-08-02 21:34:11', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (13, 'Khan Academy', 'https://khanacademy.org', '免费在线教育平台', 7, 1, 0, NULL, '2025-08-02 21:34:11', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (14, 'Notion', 'https://notion.so', '全能工作空间和笔记工具', 8, 1, 0, NULL, '2025-08-02 21:34:11', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (15, 'Trello', 'https://trello.com', '看板式项目管理工具', 8, 1, 0, NULL, '2025-08-02 21:34:11', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (16, 'Google Drive', 'https://drive.google.com', '云端文件存储和协作', 8, 1, 0, NULL, '2025-08-02 21:34:11', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (17, 'Slack', 'https://slack.com', '团队沟通协作平台', 8, 1, 0, NULL, '2025-08-02 21:34:11', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (18, 'Twitter', 'https://twitter.com', '微博社交平台', 9, 1, 0, NULL, '2025-08-02 21:34:11', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (19, 'LinkedIn', 'https://linkedin.com', '职业社交网络', 9, 1, 0, NULL, '2025-08-02 21:34:11', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (20, 'Reddit', 'https://reddit.com', '社区讨论平台', 9, 1, 0, NULL, '2025-08-02 21:34:11', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (21, 'Google Translate', 'https://translate.google.com', '在线翻译服务', 10, 1, 0, NULL, '2025-08-02 21:34:11', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (22, 'Canva', 'https://canva.com', '在线设计制作工具', 10, 1, 0, NULL, '2025-08-02 21:34:11', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (23, 'Zoom', 'https://zoom.us', '视频会议平台', 10, 1, 0, NULL, '2025-08-02 21:34:11', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (24, '测试链接', 'https://test.com', NULL, 1, 1, 0, NULL, '2025-08-02 23:11:30', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (25, 'test', 'http://www.baidu.com', '1', 5, 1, 0, 1, '2025-08-02 23:15:35', '2025-08-03 05:14:23');
INSERT INTO `links` VALUES (26, '测试网址3', 'http://example.com/path', NULL, 3, 1, 0, NULL, '2025-08-03 01:36:33', '2025-08-03 05:14:23');

-- ----------------------------
-- Table structure for media
-- ----------------------------
DROP TABLE IF EXISTS `media`;
CREATE TABLE `media`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `file_path` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `file_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件MIME类型',
  `file_size` int(11) NOT NULL COMMENT '文件大小 (bytes)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '媒体文件表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of media
-- ----------------------------
INSERT INTO `media` VALUES (1, '/uploads/link_icon/1754147685006_yvalbfyisti.png', 'image/png', 589456, '2025-08-02 23:14:45', '2025-08-03 05:14:23');
INSERT INTO `media` VALUES (2, 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgICAgPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzIiIGZpbGw9IiM2NzNBQjciLz4KICAgICAgPHRleHQgeD0iMzIiIHk9IjMyIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkb21pbmFudC1iYXNlbGluZT0iY2VudHJhbCIgCiAgICAgICAgICAgIGZpbGw9IiNGRkZGRkYiIGZvbnQtZmFtaWx5PSJzeXN0ZW0tdWksIC1hcHBsZS1zeXN0ZW0sIHNhbnMtc2VyaWYiIAogICAgICAgICAgICBmb250LXNpemU9IjI2IiBmb250LXdlaWdodD0iNTAwIj4KICAgICAgICAzCiAgICAgIDwvdGV4dD4KICAgIDwvc3ZnPg==', 'image/svg+xml', 373, '2025-08-03 05:14:58', '2025-08-03 05:14:58');

-- ----------------------------
-- Table structure for permissions
-- ----------------------------
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限名称 (e.g., users:create)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '原子权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of permissions
-- ----------------------------
INSERT INTO `permissions` VALUES (1, 'admin:dashboard:view');
INSERT INTO `permissions` VALUES (11, 'categories:create');
INSERT INTO `permissions` VALUES (13, 'categories:delete');
INSERT INTO `permissions` VALUES (12, 'categories:edit');
INSERT INTO `permissions` VALUES (10, 'categories:list');
INSERT INTO `permissions` VALUES (15, 'links:create');
INSERT INTO `permissions` VALUES (17, 'links:delete');
INSERT INTO `permissions` VALUES (16, 'links:edit');
INSERT INTO `permissions` VALUES (14, 'links:list');
INSERT INTO `permissions` VALUES (18, 'logs:view');
INSERT INTO `permissions` VALUES (7, 'roles:create');
INSERT INTO `permissions` VALUES (9, 'roles:delete');
INSERT INTO `permissions` VALUES (8, 'roles:edit');
INSERT INTO `permissions` VALUES (6, 'roles:list');
INSERT INTO `permissions` VALUES (3, 'users:create');
INSERT INTO `permissions` VALUES (5, 'users:delete');
INSERT INTO `permissions` VALUES (4, 'users:edit');
INSERT INTO `permissions` VALUES (2, 'users:list');

-- ----------------------------
-- Table structure for role_permissions
-- ----------------------------
DROP TABLE IF EXISTS `role_permissions`;
CREATE TABLE `role_permissions`  (
  `role_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`role_id`, `permission_id`) USING BTREE,
  INDEX `permission_id`(`permission_id`) USING BTREE,
  CONSTRAINT `role_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `role_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '角色与权限的关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role_permissions
-- ----------------------------
INSERT INTO `role_permissions` VALUES (1, 1);
INSERT INTO `role_permissions` VALUES (2, 1);
INSERT INTO `role_permissions` VALUES (1, 2);
INSERT INTO `role_permissions` VALUES (1, 3);
INSERT INTO `role_permissions` VALUES (1, 4);
INSERT INTO `role_permissions` VALUES (1, 5);
INSERT INTO `role_permissions` VALUES (1, 6);
INSERT INTO `role_permissions` VALUES (1, 7);
INSERT INTO `role_permissions` VALUES (1, 8);
INSERT INTO `role_permissions` VALUES (1, 9);
INSERT INTO `role_permissions` VALUES (1, 10);
INSERT INTO `role_permissions` VALUES (2, 10);
INSERT INTO `role_permissions` VALUES (1, 11);
INSERT INTO `role_permissions` VALUES (2, 11);
INSERT INTO `role_permissions` VALUES (1, 12);
INSERT INTO `role_permissions` VALUES (2, 12);
INSERT INTO `role_permissions` VALUES (1, 13);
INSERT INTO `role_permissions` VALUES (2, 13);
INSERT INTO `role_permissions` VALUES (1, 14);
INSERT INTO `role_permissions` VALUES (2, 14);
INSERT INTO `role_permissions` VALUES (1, 15);
INSERT INTO `role_permissions` VALUES (2, 15);
INSERT INTO `role_permissions` VALUES (1, 16);
INSERT INTO `role_permissions` VALUES (2, 16);
INSERT INTO `role_permissions` VALUES (1, 17);
INSERT INTO `role_permissions` VALUES (2, 17);
INSERT INTO `role_permissions` VALUES (1, 18);

-- ----------------------------
-- Table structure for roles
-- ----------------------------
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称 (super_admin, admin, user)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户角色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of roles
-- ----------------------------
INSERT INTO `roles` VALUES (2, 'admin');
INSERT INTO `roles` VALUES (1, 'super_admin');
INSERT INTO `roles` VALUES (3, 'user');

-- ----------------------------
-- Table structure for sessions
-- ----------------------------
DROP TABLE IF EXISTS `sessions`;
CREATE TABLE `sessions`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` int(11) NOT NULL,
  `expires_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_expires_at`(`expires_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sessions
-- ----------------------------

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '加密后的密码',
  `role_id` int(11) NOT NULL COMMENT '角色ID',
  `avatar_media_id` int(11) NULL DEFAULT NULL COMMENT '头像媒体文件ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username`) USING BTREE,
  UNIQUE INDEX `email`(`email`) USING BTREE,
  INDEX `role_id`(`role_id`) USING BTREE,
  INDEX `avatar_media_id`(`avatar_media_id`) USING BTREE,
  CONSTRAINT `users_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `users_ibfk_2` FOREIGN KEY (`avatar_media_id`) REFERENCES `media` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES (1, 'admin', '<EMAIL>', '$2b$10$KfiW/QJc2BVQhI5yGPgzhOHjDlPcHXEszlGHrgUWIfc3cQ8bAWjaq', 1, NULL, '2025-07-30 21:19:45', '2025-08-02 22:47:08');
INSERT INTO `users` VALUES (2, 'testuser', '<EMAIL>', '$2b$10$q6yR9zcAXL9O8G.vPbV3Le5TpQybHEOlAm03pH/l2iy.Ys7X0psBu', 3, NULL, '2025-08-02 16:27:36', '2025-08-02 16:27:36');
INSERT INTO `users` VALUES (3, 'testuser2', '<EMAIL>', '$2b$10$yoI0qG59SRWy2ltov6FlD.VnwfeY250U6IfcYjw/SrUYYckF6QzKy', 3, NULL, '2025-08-02 17:44:01', '2025-08-02 17:44:01');
INSERT INTO `users` VALUES (5, 'testuser4', '<EMAIL>', '$2b$10$1VUNhsPoAdH9KkmtcmpCTeE2xeRENsL5Mtrw73NOQdj6n92BDnMJ6', 3, NULL, '2025-08-02 17:44:45', '2025-08-02 17:44:45');
INSERT INTO `users` VALUES (6, 'newuser', '<EMAIL>', '$2b$10$Liirwz0q6iHkzlMUp9qCFOz3po8.N9ydD5rxdjV8dPnlW1i6/3FYS', 3, NULL, '2025-08-02 19:41:52', '2025-08-02 19:41:52');
INSERT INTO `users` VALUES (8, 'admin2', '<EMAIL>', '$2b$10$E.FF16SLOuYD4Js/JAmHR.eDip3A/ozWtyNiHIGGD2juSyU60w9Da', 1, NULL, '2025-08-02 19:43:33', '2025-08-02 19:43:47');

SET FOREIGN_KEY_CHECKS = 1;
