'use client';

import { AlertCircle, CheckCircle, Info, Loader2, Search, FolderOpen } from 'lucide-react';
import { Button } from '@/components/ui/Button';

// 通用加载组件
interface LoadingProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'spinner' | 'skeleton';
}

export function Loading({ 
  message = '加载中...', 
  size = 'md',
  variant = 'spinner' 
}: LoadingProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8', 
    lg: 'h-12 w-12'
  };

  if (variant === 'skeleton') {
    return (
      <div className="animate-pulse space-y-4">
        <div className="h-4 bg-muted rounded w-3/4"></div>
        <div className="h-4 bg-muted rounded w-1/2"></div>
        <div className="h-4 bg-muted rounded w-5/6"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      <Loader2 className={`${sizeClasses[size]} animate-spin text-primary mb-4`} />
      <p className="text-muted-foreground">{message}</p>
    </div>
  );
}

// 通用错误组件
interface ErrorStateProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  showRetry?: boolean;
}

export function ErrorState({ 
  title = '出现错误',
  message = '加载数据时发生错误，请稍后重试',
  onRetry,
  showRetry = true
}: ErrorStateProps) {
  return (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      <div className="w-16 h-16 bg-red-50 rounded-full flex items-center justify-center mb-4">
        <AlertCircle className="w-8 h-8 text-red-600" />
      </div>
      <h3 className="text-lg font-semibold text-foreground mb-2">{title}</h3>
      <p className="text-muted-foreground mb-6 max-w-md">{message}</p>
      {showRetry && onRetry && (
        <Button onClick={onRetry} variant="outline">
          重新加载
        </Button>
      )}
    </div>
  );
}

// 通用空状态组件
interface EmptyStateProps {
  title?: string;
  message?: string;
  icon?: 'search' | 'folder' | 'generic';
  action?: {
    label: string;
    onClick: () => void;
  };
}

export function EmptyState({ 
  title = '暂无数据',
  message = '当前没有找到相关内容',
  icon = 'generic',
  action
}: EmptyStateProps) {
  const icons = {
    search: Search,
    folder: FolderOpen,
    generic: Info
  };

  const IconComponent = icons[icon];

  return (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      <div className="w-16 h-16 bg-muted/50 rounded-full flex items-center justify-center mb-4">
        <IconComponent className="w-8 h-8 text-muted-foreground" />
      </div>
      <h3 className="text-lg font-semibold text-foreground mb-2">{title}</h3>
      <p className="text-muted-foreground mb-6 max-w-md">{message}</p>
      {action && (
        <Button onClick={action.onClick}>
          {action.label}
        </Button>
      )}
    </div>
  );
}

// 通用成功状态组件
interface SuccessStateProps {
  title?: string;
  message?: string;
  onContinue?: () => void;
  continueLabel?: string;
}

export function SuccessState({ 
  title = '操作成功',
  message = '操作已成功完成',
  onContinue,
  continueLabel = '继续'
}: SuccessStateProps) {
  return (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      <div className="w-16 h-16 bg-green-50 rounded-full flex items-center justify-center mb-4">
        <CheckCircle className="w-8 h-8 text-green-600" />
      </div>
      <h3 className="text-lg font-semibold text-foreground mb-2">{title}</h3>
      <p className="text-muted-foreground mb-6 max-w-md">{message}</p>
      {onContinue && (
        <Button onClick={onContinue}>
          {continueLabel}
        </Button>
      )}
    </div>
  );
}

// 通用页面容器组件
interface PageContainerProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  actions?: React.ReactNode;
  className?: string;
}

export function PageContainer({ 
  children, 
  title, 
  description, 
  actions,
  className = ''
}: PageContainerProps) {
  return (
    <div className={`min-h-screen bg-background ${className}`}>
      {(title || description || actions) && (
        <div className="border-b border-border bg-background/50 backdrop-blur-sm sticky top-0 z-10">
          <div className="container mx-auto px-4 py-6">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div>
                {title && (
                  <h1 className="text-3xl font-bold text-foreground">{title}</h1>
                )}
                {description && (
                  <p className="text-muted-foreground mt-2">{description}</p>
                )}
              </div>
              {actions && (
                <div className="flex items-center gap-2">
                  {actions}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
      <div className="container mx-auto px-4 py-6">
        {children}
      </div>
    </div>
  );
}

// 通用内容包装器
interface ContentWrapperProps {
  children: React.ReactNode;
  loading?: boolean;
  error?: string;
  empty?: boolean;
  emptyProps?: Partial<EmptyStateProps>;
  onRetry?: () => void;
  className?: string;
}

export function ContentWrapper({ 
  children, 
  loading = false,
  error,
  empty = false,
  emptyProps = {},
  onRetry,
  className = ''
}: ContentWrapperProps) {
  if (loading) {
    return <Loading />;
  }

  if (error) {
    return <ErrorState message={error} onRetry={onRetry} />;
  }

  if (empty) {
    return <EmptyState {...emptyProps} />;
  }

  return <div className={className}>{children}</div>;
}