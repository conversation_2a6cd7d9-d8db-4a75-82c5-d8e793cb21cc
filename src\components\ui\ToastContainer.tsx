'use client';

import { createPortal } from 'react-dom';
import { useEffect, useState } from 'react';
import Toast from './Toast';
import { ToastMessage } from '@/types/toast';

interface ToastContainerProps {
  toasts: ToastMessage[];
  onClose: (id: string) => void;
}

export default function ToastContainer({ toasts, onClose }: ToastContainerProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  const portalElement = document.getElementById('toast-portal') || document.body;

  return createPortal(
    <div 
      className="fixed top-4 right-4 z-[9999] space-y-2 pointer-events-none"
      style={{ maxWidth: 'calc(100vw - 2rem)' }}
    >
      <style jsx global>{`
        /* 移动端适配 */
        @media (max-width: 640px) {
          .toast-container {
            top: 1rem !important;
            left: 1rem !important;
            right: 1rem !important;
            max-width: none !important;
          }
        }
      `}</style>
      
      <div className="toast-container space-y-2">
        {toasts.map((toast, index) => (
          <div
            key={toast.id}
            className="pointer-events-auto"
            style={{
              animationDelay: `${index * 100}ms`,
              zIndex: 9999 - index,
            }}
          >
            <Toast
              toast={toast}
              onClose={onClose}
            />
          </div>
        ))}
      </div>
    </div>,
    portalElement
  );
}