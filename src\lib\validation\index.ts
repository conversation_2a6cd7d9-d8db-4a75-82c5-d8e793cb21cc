import { z } from 'zod';

export const loginSchema = z.object({
  username: z.string().min(1, '用户名不能为空'),
  password: z.string().min(1, '密码不能为空'),
  remember: z.boolean().optional().default(false),
});

export const registerSchema = z.object({
  username: z.string().min(3, '用户名至少3个字符').max(20, '用户名最多20个字符'),
  email: z.string().email('邮箱格式不正确'),
  password: z.string().min(6, '密码至少6个字符'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: '两次密码输入不一致',
  path: ['confirmPassword'],
});

export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, '当前密码不能为空'),
  newPassword: z.string().min(6, '新密码至少6个字符'),
  confirmPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: '两次密码输入不一致',
  path: ['confirmPassword'],
});

export const createUserSchema = z.object({
  username: z.string().min(3, '用户名至少3个字符').max(20, '用户名最多20个字符'),
  email: z.string().email('邮箱格式不正确'),
  password: z.string().min(6, '密码至少6个字符'),
  role: z.enum(['super_admin', 'admin', 'user']),
  avatar: z.string().optional(),
});

export const updateUserSchema = z.object({
  username: z.string().min(3, '用户名至少3个字符').max(20, '用户名最多20个字符').optional(),
  email: z.string().email('邮箱格式不正确').optional(),
  role: z.enum(['super_admin', 'admin', 'user']).optional(),
  avatar: z.string().optional(),
});

export const createCategorySchema = z.object({
  name: z.string().min(1, '分类名称不能为空').max(50, '分类名称最多50个字符'),
  order: z.number().int().min(0).optional().default(0),
  icon: z.string().optional(),
  isPrivate: z.boolean().optional().default(false),
  parentId: z.number().int().positive().optional(),
});

export const updateCategorySchema = z.object({
  name: z.string().min(1, '分类名称不能为空').max(50, '分类名称最多50个字符').optional(),
  order: z.number().int().min(0).optional(),
  icon: z.string().optional(),
  isPrivate: z.boolean().optional(),
  parentId: z.number().int().positive().optional(),
});

export const createLinkSchema = z.object({
  title: z.string().min(1, '链接标题不能为空').max(255, '链接标题最多255个字符'),
  url: z.string().url('URL格式不正确'),
  description: z.string().max(1000, '描述最多1000个字符').optional(),
  categoryId: z.number().int().positive('分类ID必须为正整数'),
  isPrivate: z.boolean().optional().default(false),
  icon: z.string().optional(),
});

export const updateLinkSchema = z.object({
  title: z.string().min(1, '链接标题不能为空').max(255, '链接标题最多255个字符').optional(),
  url: z.string().url('URL格式不正确').optional(),
  description: z.string().max(1000, '描述最多1000个字符').optional(),
  categoryId: z.number().int().positive('分类ID必须为正整数').optional(),
  isPrivate: z.boolean().optional(),
  icon: z.string().optional(),
});

export const quickAddLinkSchema = z.object({
  url: z.string().url('URL格式不正确'),
  categoryId: z.number().int().positive('分类ID必须为正整数'),
  isPrivate: z.boolean().optional().default(false),
});

export const batchOperationSchema = z.object({
  action: z.enum(['delete', 'move', 'privacy']),
  ids: z.array(z.number().int().positive()).min(1, '至少选择一个项目'),
  categoryId: z.number().int().positive().optional(),
  isPrivate: z.boolean().optional(),
}).refine((data) => {
  if (data.action === 'move' && !data.categoryId) {
    return false;
  }
  if (data.action === 'privacy' && typeof data.isPrivate !== 'boolean') {
    return false;
  }
  return true;
}, {
  message: '操作参数不完整',
  path: ['action'],
});

export const reorderCategoriesSchema = z.object({
  categories: z.array(z.object({
    id: z.number().int().positive(),
    order: z.number().int().min(0),
  })).min(1, '至少需要一个分类'),
});

export const searchSchema = z.object({
  q: z.string().min(1, '搜索关键词不能为空'),
  page: z.number().int().positive().optional().default(1),
  limit: z.number().int().positive().max(100).optional().default(10),
  categoryId: z.number().int().positive().optional(),
  isPrivate: z.boolean().optional(),
});

export const systemSettingsSchema = z.object({
  siteName: z.string().min(1, '网站名称不能为空').max(100, '网站名称最多100个字符'),
  siteDescription: z.string().max(500, '网站描述最多500个字符').optional(),
  siteLogo: z.string().optional(),
  allowRegistration: z.boolean().optional().default(true),
  defaultRole: z.enum(['super_admin', 'admin', 'user']).optional().default('user'),
  itemsPerPage: z.number().int().positive().max(100).optional().default(10),
  maxFileSize: z.number().int().positive().optional().default(10485760),
  allowedFileTypes: z.array(z.string()).optional().default(['image/jpeg', 'image/png', 'image/gif']),
});

export type LoginInput = z.infer<typeof loginSchema>;
export type RegisterInput = z.infer<typeof registerSchema>;
export type ChangePasswordInput = z.infer<typeof changePasswordSchema>;
export type CreateUserInput = z.infer<typeof createUserSchema>;
export type UpdateUserInput = z.infer<typeof updateUserSchema>;
export type CreateCategoryInput = z.infer<typeof createCategorySchema>;
export type UpdateCategoryInput = z.infer<typeof updateCategorySchema>;
export type CreateLinkInput = z.infer<typeof createLinkSchema>;
export type UpdateLinkInput = z.infer<typeof updateLinkSchema>;
export type QuickAddLinkInput = z.infer<typeof quickAddLinkSchema>;
export type BatchOperationInput = z.infer<typeof batchOperationSchema>;
export type ReorderCategoriesInput = z.infer<typeof reorderCategoriesSchema>;
export type SearchInput = z.infer<typeof searchSchema>;
export type SystemSettingsInput = z.infer<typeof systemSettingsSchema>;