'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Settings } from 'lucide-react';

interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  avatar?: string;
}

export default function AdminDashboardWrapper() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      // 检查用户是否已登录
      const response = await fetch('/api/auth/me');
      if (response.ok) {
        const data = await response.json();
        if (data.success && (data.data.user.role === 'super_admin' || data.data.user.role === 'admin')) {
          setUser(data.data.user);
        } else {
          router.push('/admin/login');
        }
      } else {
        router.push('/admin/login');
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      router.push('/admin/login');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">仪表板</h1>
        <p className="text-muted-foreground">
          欢迎回来，{user?.username}！
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">用户管理</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">管理</div>
            <p className="text-xs text-muted-foreground">
              用户权限管理
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">内容管理</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">管理</div>
            <p className="text-xs text-muted-foreground">
              网站内容管理
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">系统设置</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">设置</div>
            <p className="text-xs text-muted-foreground">
              系统配置管理
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>快速操作</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <a
                href="/admin/users"
                className="p-4 border border-border rounded-lg hover:bg-muted transition-colors"
              >
                <Settings className="h-8 w-8 mb-2 text-primary" />
                <p className="text-sm font-medium">用户管理</p>
              </a>
              <a
                href="/admin/permissions"
                className="p-4 border border-border rounded-lg hover:bg-muted transition-colors"
              >
                <Settings className="h-8 w-8 mb-2 text-primary" />
                <p className="text-sm font-medium">权限设置</p>
              </a>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>系统信息</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">用户角色:</span>
                <span className="text-sm font-medium">{user?.role}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">用户邮箱:</span>
                <span className="text-sm font-medium">{user?.email}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">系统状态:</span>
                <span className="text-sm font-medium text-green-600">正常运行</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}