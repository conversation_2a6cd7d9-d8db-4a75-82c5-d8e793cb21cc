# API接口文档

## 接口概述

### 基础信息
- **基础URL**：`/api`
- **内容类型**：`application/json`
- **认证方式**：Bearer <PERSON>ken (JWT) + Session
- **字符编码**：UTF-8

### 响应格式标准
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "code": 200,
  "timestamp": "2025-08-04T12:00:00Z"
}
```

### 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误信息",
    "details": {}
  },
  "timestamp": "2025-08-04T12:00:00Z"
}
```

## 认证相关接口

### 1. 用户登录
- **URL**：`/auth/login`
- **方法**：`POST`
- **描述**：用户登录接口

#### 请求参数
```json
{
  "username": "string",
  "password": "string",
  "remember": "boolean"
}
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "role": "super_admin",
      "avatar": null,
      "createdAt": "2025-07-30T21:19:45Z"
    },
    "token": "jwt_token_here",
    "permissions": [
      "users:create",
      "users:edit",
      "users:delete",
      "users:list"
    ]
  },
  "message": "登录成功"
}
```

### 2. 用户注册
- **URL**：`/auth/register`
- **方法**：`POST`
- **描述**：用户注册接口

#### 请求参数
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "confirmPassword": "string"
}
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 2,
      "username": "newuser",
      "email": "<EMAIL>",
      "role": "user",
      "avatar": null,
      "createdAt": "2025-08-04T12:00:00Z"
    }
  },
  "message": "注册成功"
}
```

### 3. 用户登出
- **URL**：`/auth/logout`
- **方法**：`POST`
- **描述**：用户登出接口

#### 响应示例
```json
{
  "success": true,
  "message": "登出成功"
}
```

### 4. 获取当前用户信息
- **URL**：`/auth/me`
- **方法**：`GET`
- **描述**：获取当前登录用户信息

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "role": "super_admin",
    "permissions": [
      "users:create",
      "users:edit",
      "users:delete",
      "users:list"
    ],
    "avatar": null,
    "createdAt": "2025-07-30T21:19:45Z"
  }
}
```

### 5. 修改密码
- **URL**：`/auth/change-password`
- **方法**：`POST`
- **描述**：修改用户密码

#### 请求参数
```json
{
  "currentPassword": "string",
  "newPassword": "string",
  "confirmPassword": "string"
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "密码修改成功"
}
```

## 用户管理接口

### 1. 获取用户列表
- **URL**：`/users`
- **方法**：`GET`
- **描述**：获取用户列表（分页）

#### 查询参数
- `page`：页码（默认1）
- `limit`：每页数量（默认10）
- `search`：搜索关键词
- `role`：角色筛选
- `sortBy`：排序字段
- `sortOrder`：排序方向（asc/desc）

#### 响应示例
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "role": "super_admin",
        "avatar": null,
        "createdAt": "2025-07-30T21:19:45Z",
        "lastLogin": "2025-08-04T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

### 2. 获取用户详情
- **URL**：`/users/:id`
- **方法**：`GET`
- **描述**：获取用户详细信息

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "role": "super_admin",
    "avatar": null,
    "permissions": [
      "users:create",
      "users:edit",
      "users:delete",
      "users:list"
    ],
    "createdAt": "2025-07-30T21:19:45Z",
    "updatedAt": "2025-08-02T22:47:08Z",
    "lastLogin": "2025-08-04T10:30:00Z"
  }
}
```

### 3. 创建用户
- **URL**：`/users`
- **方法**：`POST`
- **描述**：创建新用户

#### 请求参数
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "role": "string",
  "avatar": "string"
}
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 10,
    "username": "newuser",
    "email": "<EMAIL>",
    "role": "user",
    "avatar": null,
    "createdAt": "2025-08-04T12:00:00Z"
  },
  "message": "用户创建成功"
}
```

### 4. 更新用户
- **URL**：`/users/:id`
- **方法**：`PUT`
- **描述**：更新用户信息

#### 请求参数
```json
{
  "username": "string",
  "email": "string",
  "role": "string",
  "avatar": "string"
}
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "role": "super_admin",
    "avatar": null,
    "updatedAt": "2025-08-04T12:00:00Z"
  },
  "message": "用户更新成功"
}
```

### 5. 删除用户
- **URL**：`/users/:id`
- **方法**：`DELETE`
- **描述**：删除用户

#### 响应示例
```json
{
  "success": true,
  "message": "用户删除成功"
}
```

### 6. 批量删除用户
- **URL**：`/users/batch`
- **方法**：`DELETE`
- **描述**：批量删除用户

#### 请求参数
```json
{
  "ids": [1, 2, 3]
}
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "deletedCount": 3
  },
  "message": "批量删除成功"
}
```

## 分类管理接口

### 1. 获取分类列表
- **URL**：`/categories`
- **方法**：`GET`
- **描述**：获取分类列表

#### 查询参数
- `page`：页码（默认1）
- `limit`：每页数量（默认10）
- `search`：搜索关键词
- `parentId`：父级分类ID
- `isPrivate`：是否私有
- `sortBy`：排序字段
- `sortOrder`：排序方向

#### 响应示例
```json
{
  "success": true,
  "data": {
    "categories": [
      {
        "id": 1,
        "name": "常用工具",
        "order": 4,
        "icon": null,
        "isPrivate": false,
        "parentId": null,
        "children": [],
        "createdAt": "2025-08-03T05:14:24Z",
        "updatedAt": "2025-08-03T05:30:02Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 12,
      "totalPages": 2
    }
  }
}
```

### 2. 获取分类树
- **URL**：`/categories/tree`
- **方法**：`GET`
- **描述**：获取分类树结构

#### 响应示例
```json
{
  "success": true,
  "data": {
    "categories": [
      {
        "id": 1,
        "name": "常用工具",
        "order": 4,
        "icon": null,
        "isPrivate": false,
        "children": [
          {
            "id": 5,
            "name": "开发工具",
            "order": 1,
            "icon": null,
            "isPrivate": false,
            "children": []
          }
        ]
      }
    ]
  }
}
```

### 3. 获取分类详情
- **URL**：`/categories/:id`
- **方法**：`GET`
- **描述**：获取分类详细信息

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "常用工具",
    "order": 4,
    "icon": null,
    "isPrivate": false,
    "parentId": null,
    "children": [],
    "linksCount": 5,
    "createdAt": "2025-08-03T05:14:24Z",
    "updatedAt": "2025-08-03T05:30:02Z"
  }
}
```

### 4. 创建分类
- **URL**：`/categories`
- **方法**：`POST`
- **描述**：创建新分类

#### 请求参数
```json
{
  "name": "string",
  "order": 0,
  "icon": "string",
  "isPrivate": false,
  "parentId": null
}
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 13,
    "name": "新分类",
    "order": 0,
    "icon": null,
    "isPrivate": false,
    "parentId": null,
    "createdAt": "2025-08-04T12:00:00Z"
  },
  "message": "分类创建成功"
}
```

### 5. 更新分类
- **URL**：`/categories/:id`
- **方法**：`PUT`
- **描述**：更新分类信息

#### 请求参数
```json
{
  "name": "string",
  "order": 0,
  "icon": "string",
  "isPrivate": false,
  "parentId": null
}
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "常用工具",
    "order": 4,
    "icon": null,
    "isPrivate": false,
    "updatedAt": "2025-08-04T12:00:00Z"
  },
  "message": "分类更新成功"
}
```

### 6. 删除分类
- **URL**：`/categories/:id`
- **方法**：`DELETE`
- **描述**：删除分类

#### 响应示例
```json
{
  "success": true,
  "message": "分类删除成功"
}
```

### 7. 更新分类排序
- **URL**：`/categories/reorder`
- **方法**：`PUT`
- **描述**：更新分类排序

#### 请求参数
```json
{
  "categories": [
    {
      "id": 1,
      "order": 1
    },
    {
      "id": 2,
      "order": 2
    }
  ]
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "分类排序更新成功"
}
```

## 网站管理接口

### 1. 获取网站列表
- **URL**：`/links`
- **方法**：`GET`
- **描述**：获取网站列表

#### 查询参数
- `page`：页码（默认1）
- `limit`：每页数量（默认10）
- `search`：搜索关键词
- `categoryId`：分类ID
- `isPrivate`：是否私有
- `userId`：用户ID
- `sortBy`：排序字段
- `sortOrder`：排序方向

#### 响应示例
```json
{
  "success": true,
  "data": {
    "websites": [
      {
        "id": 2,
        "title": "GitHub",
        "url": "https://github.com",
        "description": "全球最大的代码托管平台",
        "category": {
          "id": 5,
          "name": "开发工具"
        },
        "icon": null,
        "isPrivate": false,
        "userId": 1,
        "createdAt": "2025-08-02T21:34:11Z",
        "updatedAt": "2025-08-03T05:14:23Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

### 2. 获取网站详情
- **URL**：`/links/:id`
- **方法**：`GET`
- **描述**：获取网站详细信息

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 2,
    "title": "GitHub",
    "url": "https://github.com",
    "description": "全球最大的代码托管平台",
    "category": {
      "id": 5,
      "name": "开发工具"
    },
    "icon": null,
    "isPrivate": false,
    "userId": 1,
    "user": {
      "id": 1,
      "username": "admin"
    },
    "createdAt": "2025-08-02T21:34:11Z",
    "updatedAt": "2025-08-03T05:14:23Z"
  }
}
```

### 3. 创建网站
- **URL**：`/links`
- **方法**：`POST`
- **描述**：创建新网站

#### 请求参数
```json
{
  "title": "string",
  "url": "string",
  "description": "string",
  "categoryId": 1,
  "isPrivate": false,
  "icon": "string"
}
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 28,
    "title": "新网站",
    "url": "https://example.com",
    "description": "网站描述",
    "categoryId": 1,
    "icon": null,
    "isPrivate": false,
    "userId": 1,
    "createdAt": "2025-08-04T12:00:00Z"
  },
  "message": "网站创建成功"
}
```

### 4. 更新网站
- **URL**：`/links/:id`
- **方法**：`PUT`
- **描述**：更新网站信息

#### 请求参数
```json
{
  "title": "string",
  "url": "string",
  "description": "string",
  "categoryId": 1,
  "isPrivate": false,
  "icon": "string"
}
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 2,
    "title": "GitHub",
    "url": "https://github.com",
    "description": "全球最大的代码托管平台",
    "categoryId": 5,
    "icon": null,
    "isPrivate": false,
    "updatedAt": "2025-08-04T12:00:00Z"
  },
  "message": "网站更新成功"
}
```

### 5. 删除网站
- **URL**：`/links/:id`
- **方法**：`DELETE`
- **描述**：删除网站

#### 响应示例
```json
{
  "success": true,
  "message": "网站删除成功"
}
```

### 6. 批量操作链接
- **URL**：`/links/batch`
- **方法**：`POST`
- **描述**：批量操作链接

#### 请求参数
```json
{
  "action": "delete|move|privacy",
  "ids": [1, 2, 3],
  "categoryId": 1,
  "isPrivate": false
}
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "processedCount": 3
  },
  "message": "批量操作成功"
}
```

### 7. 快速添加链接
- **URL**：`/links/quick-add`
- **方法**：`POST`
- **描述**：快速添加链接（自动获取标题和描述）

#### 请求参数
```json
{
  "url": "string",
  "categoryId": 1,
  "isPrivate": false
}
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 29,
    "title": "网站标题",
    "url": "https://example.com",
    "description": "网站描述",
    "categoryId": 1,
    "icon": null,
    "isPrivate": false,
    "userId": 1,
    "createdAt": "2025-08-04T12:00:00Z"
  },
  "message": "网站添加成功"
}
```

## 文件上传接口

### 1. 上传文件
- **URL**：`/upload`
- **方法**：`POST`
- **描述**：上传文件

#### 请求参数
- `file`：文件对象（multipart/form-data）
- `type`：文件类型（avatar|icon|other）

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 4,
    "filePath": "/uploads/avatar/1722765600000_filename.jpg",
    "fileType": "image/jpeg",
    "fileSize": 102400,
    "url": "/uploads/avatar/1722765600000_filename.jpg"
  },
  "message": "文件上传成功"
}
```

### 2. 生成图标
- **URL**：`/upload/generate-icon`
- **方法**：`POST`
- **描述**：生成网站图标

#### 请求参数
```json
{
  "title": "string",
  "url": "string"
}
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 5,
    "filePath": "data:image/svg+xml;base64,...",
    "fileType": "image/svg+xml",
    "fileSize": 373,
    "url": "data:image/svg+xml;base64,..."
  },
  "message": "图标生成成功"
}
```

### 3. 删除文件
- **URL**：`/upload/:id`
- **方法**：`DELETE`
- **描述**：删除文件

#### 响应示例
```json
{
  "success": true,
  "message": "文件删除成功"
}
```

## 权限管理接口

### 1. 获取权限列表
- **URL**：`/permissions`
- **方法**：`GET`
- **描述**：获取权限列表

#### 响应示例
```json
{
  "success": true,
  "data": {
    "permissions": [
      {
        "id": 1,
        "name": "admin:dashboard:view",
        "description": "查看管理面板"
      },
      {
        "id": 2,
        "name": "users:list",
        "description": "查看用户列表"
      }
    ]
  }
}
```

### 2. 获取角色列表
- **URL**：`/roles`
- **方法**：`GET`
- **描述**：获取角色列表

#### 响应示例
```json
{
  "success": true,
  "data": {
    "roles": [
      {
        "id": 1,
        "name": "super_admin",
        "description": "超级管理员",
        "permissions": [
          {
            "id": 1,
            "name": "admin:dashboard:view"
          }
        ]
      },
      {
        "id": 2,
        "name": "admin",
        "description": "管理员",
        "permissions": [
          {
            "id": 10,
            "name": "categories:list"
          }
        ]
      }
    ]
  }
}
```

### 3. 更新角色权限
- **URL**：`/roles/:roleId/permissions`
- **方法**：`PUT`
- **描述**：更新角色权限

#### 请求参数
```json
{
  "permissionIds": [1, 2, 3]
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "角色权限更新成功"
}
```

## 日志管理接口

### 1. 获取操作日志
- **URL**：`/logs`
- **方法**：`GET`
- **描述**：获取操作日志

#### 查询参数
- `page`：页码（默认1）
- `limit`：每页数量（默认10）
- `userId`：用户ID
- `action`：操作类型
- `targetType`：目标类型
- `startDate`：开始日期
- `endDate`：结束日期

#### 响应示例
```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": 1,
        "userId": 1,
        "user": {
          "username": "admin"
        },
        "action": "创建链接",
        "targetType": "link",
        "targetId": 28,
        "createdAt": "2025-08-04T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "totalPages": 10
    }
  }
}
```

### 2. 获取用户统计
- **URL**：`/logs/user-stats`
- **方法**：`GET`
- **描述**：获取用户操作统计

#### 响应示例
```json
{
  "success": true,
  "data": {
    "userStats": [
      {
        "userId": 1,
        "username": "admin",
        "actionCount": 150,
        "lastAction": "2025-08-04T12:00:00Z"
      }
    ]
  }
}
```

### 3. 获取系统统计
- **URL**：`/logs/system-stats`
- **方法**：`GET`
- **描述**：获取系统操作统计

#### 响应示例
```json
{
  "success": true,
  "data": {
    "totalActions": 1000,
    "dailyStats": [
      {
        "date": "2025-08-04",
        "count": 150
      }
    ],
    "topActions": [
      {
        "action": "创建链接",
        "count": 200
      }
    ]
  }
}
```

## 搜索接口

### 1. 搜索链接
- **URL**：`/search/links`
- **方法**：`GET`
- **描述**：搜索链接

#### 查询参数
- `q`：搜索关键词
- `page`：页码（默认1）
- `limit`：每页数量（默认10）
- `categoryId`：分类ID
- `isPrivate`：是否私有

#### 响应示例
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "id": 2,
        "title": "GitHub",
        "url": "https://github.com",
        "description": "全球最大的代码托管平台",
        "category": {
          "id": 5,
          "name": "开发工具"
        },
        "highlight": {
          "title": "<em>GitHub</em>",
          "description": "全球最大的<em>代码</em>托管平台"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 5,
      "totalPages": 1
    }
  }
}
```

### 2. 搜索建议
- **URL**：`/search/suggestions`
- **方法**：`GET`
- **描述**：获取搜索建议

#### 查询参数
- `q`：搜索关键词
- `limit`：建议数量（默认5）

#### 响应示例
```json
{
  "success": true,
  "data": {
    "suggestions": [
      {
        "text": "GitHub",
        "type": "link",
        "url": "https://github.com"
      },
      {
        "text": "开发工具",
        "type": "category",
        "url": "/category/5"
      }
    ]
  }
}
```

## 系统设置接口

### 1. 获取系统设置
- **URL**：`/settings`
- **方法**：`GET`
- **描述**：获取系统设置

#### 响应示例
```json
{
  "success": true,
  "data": {
    "siteName": "资源导航网站",
    "siteDescription": "优质的网站资源导航",
    "siteLogo": null,
    "allowRegistration": true,
    "defaultRole": "user",
    "itemsPerPage": 10,
    "maxFileSize": 10485760,
    "allowedFileTypes": ["image/jpeg", "image/png", "image/gif"]
  }
}
```

### 2. 更新系统设置
- **URL**：`/settings`
- **方法**：`PUT`
- **描述**：更新系统设置

#### 请求参数
```json
{
  "siteName": "string",
  "siteDescription": "string",
  "siteLogo": "string",
  "allowRegistration": true,
  "defaultRole": "user",
  "itemsPerPage": 10,
  "maxFileSize": 10485760,
  "allowedFileTypes": ["image/jpeg", "image/png", "image/gif"]
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "系统设置更新成功"
}
```

## 错误代码

### 认证错误
- `UNAUTHORIZED`：未授权访问
- `INVALID_TOKEN`：无效的令牌
- `TOKEN_EXPIRED`：令牌已过期
- `INVALID_CREDENTIALS`：无效的凭据
- `USER_NOT_FOUND`：用户不存在
- `USER_DISABLED`：用户已禁用

### 权限错误
- `FORBIDDEN`：权限不足
- `INSUFFICIENT_PERMISSIONS`：权限不足

### 验证错误
- `VALIDATION_ERROR`：数据验证失败
- `REQUIRED_FIELD_MISSING`：必填字段缺失
- `INVALID_FORMAT`：格式无效
- `INVALID_EMAIL`：邮箱格式无效
- `INVALID_URL`：URL格式无效
- `PASSWORD_TOO_WEAK`：密码强度不足

### 业务错误
- `USER_ALREADY_EXISTS`：用户已存在
- `CATEGORY_NOT_FOUND`：分类不存在
- `LINK_NOT_FOUND`：链接不存在
- `FILE_NOT_FOUND`：文件不存在
- `FILE_UPLOAD_FAILED`：文件上传失败
- `FILE_SIZE_EXCEEDED`：文件大小超限
- `INVALID_FILE_TYPE`：无效的文件类型

### 系统错误
- `INTERNAL_SERVER_ERROR`：服务器内部错误
- `DATABASE_ERROR`：数据库错误
- `NETWORK_ERROR`：网络错误
- `SERVICE_UNAVAILABLE`：服务不可用

## 数据模型

### 用户模型
```typescript
interface User {
  id: number;
  username: string;
  email: string;
  role: 'super_admin' | 'admin' | 'user';
  avatar?: string;
  createdAt: string;
  updatedAt: string;
  lastLogin?: string;
}
```

### 分类模型
```typescript
interface Category {
  id: number;
  name: string;
  order: number;
  icon?: string;
  isPrivate: boolean;
  parentId?: number;
  children?: Category[];
  createdAt: string;
  updatedAt: string;
}
```

### 链接模型
```typescript
interface Link {
  id: number;
  title: string;
  url: string;
  description?: string;
  categoryId: number;
  category?: Category;
  icon?: string;
  isPrivate: boolean;
  userId: number;
  user?: User;
  createdAt: string;
  updatedAt: string;
}
```

### 媒体文件模型
```typescript
interface Media {
  id: number;
  filePath: string;
  fileType: string;
  fileSize: number;
  createdAt: string;
  updatedAt: string;
}
```

### 权限模型
```typescript
interface Permission {
  id: number;
  name: string;
  description?: string;
}
```

### 角色模型
```typescript
interface Role {
  id: number;
  name: string;
  description?: string;
  permissions?: Permission[];
}
```

### 操作日志模型
```typescript
interface ActivityLog {
  id: number;
  userId: number;
  user?: User;
  action: string;
  targetType?: string;
  targetId?: number;
  createdAt: string;
}
```

---

**文档版本**：1.0  
**创建日期**：2025-08-04  
**最后更新**：2025-08-04  
**文档状态**：初稿完成  
**负责人**：开发团队