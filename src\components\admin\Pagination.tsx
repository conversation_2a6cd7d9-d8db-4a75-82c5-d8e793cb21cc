'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  total: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  loading?: boolean;
  className?: string;
}

export function Pagination({
  currentPage,
  totalPages,
  total,
  pageSize,
  onPageChange,
  onPageSizeChange,
  loading = false,
  className = ''
}: PaginationProps) {
  const [jumpPage, setJumpPage] = useState('');

  // 计算显示范围
  const startItem = (currentPage - 1) * pageSize + 1;
  const endItem = Math.min(currentPage * pageSize, total);

  // 跳转到指定页面
  const handleJumpToPage = () => {
    const pageNum = parseInt(jumpPage);
    if (pageNum && pageNum >= 1 && pageNum <= totalPages) {
      onPageChange(pageNum);
      setJumpPage('');
    }
  };

  // 处理跳转输入框回车事件
  const handleJumpKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleJumpToPage();
    }
  };

  // 页大小选项
  const pageSizeOptions = [10, 25, 50, 100];

  // 生成页码按钮
  const renderPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 7;

    if (totalPages <= maxVisiblePages) {
      // 如果总页数小于等于最大显示页数，显示所有页码
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // 复杂分页逻辑
      const startPage = Math.max(1, currentPage - 2);
      const endPage = Math.min(totalPages, currentPage + 2);

      if (startPage > 1) {
        pages.push(1);
        if (startPage > 2) {
          pages.push('...');
        }
      }

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }

      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          pages.push('...');
        }
        pages.push(totalPages);
      }
    }

    return pages.map((page, index) => {
      if (page === '...') {
        return (
          <span key={`ellipsis-${index}`} className="px-2 py-1 text-muted-foreground">
            ...
          </span>
        );
      }

      const pageNum = page as number;
      const isActive = pageNum === currentPage;

      return (
        <Button
          key={pageNum}
          variant={isActive ? 'default' : 'outline'}
          size="sm"
          onClick={() => onPageChange(pageNum)}
          disabled={loading}
          className={`min-w-[36px] h-9 ${isActive ? 'pointer-events-none' : ''}`}
        >
          {pageNum}
        </Button>
      );
    });
  };

  if (totalPages <= 1) {
    return null;
  }

  return (
    <div className={`flex flex-col space-y-4 ${className}`}>
      {/* 信息统计 - 完全居中 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-center space-y-3 sm:space-y-0 sm:space-x-8 text-sm text-muted-foreground">
        <div className="text-center">
          显示第 <span className="font-medium text-foreground">{startItem}</span> - <span className="font-medium text-foreground">{endItem}</span> 条，
          共 <span className="font-medium text-foreground">{total}</span> 条记录
        </div>
        
        {/* 每页显示数量选择 */}
        <div className="flex items-center justify-center space-x-2">
          <span className="whitespace-nowrap">每页显示</span>
          <select
            value={pageSize}
            onChange={(e) => onPageSizeChange(parseInt(e.target.value))}
            disabled={loading}
            className="px-2 py-1 border border-border rounded bg-background text-foreground text-sm min-w-0"
          >
            {pageSizeOptions.map(size => (
              <option key={size} value={size}>
                {size}
              </option>
            ))}
          </select>
          <span className="whitespace-nowrap">条</span>
        </div>
      </div>

      {/* 分页控件 - 完全居中 */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-center space-y-3 lg:space-y-0 lg:space-x-8">
        {/* 页码导航 */}
        <div className="flex items-center justify-center space-x-2">
          {/* 首页按钮 */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(1)}
            disabled={currentPage === 1 || loading}
            className="h-9 px-2"
            title="首页"
          >
            <ChevronsLeft className="w-4 h-4" />
          </Button>

          {/* 上一页按钮 */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1 || loading}
            className="h-9"
            title="上一页"
          >
            <ChevronLeft className="w-4 h-4 mr-1" />
            <span className="hidden sm:inline">上一页</span>
          </Button>

          {/* 页码按钮 */}
          <div className="flex items-center space-x-1">
            {renderPageNumbers()}
          </div>

          {/* 下一页按钮 */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages || loading}
            className="h-9"
            title="下一页"
          >
            <span className="hidden sm:inline">下一页</span>
            <ChevronRight className="w-4 h-4 ml-1" />
          </Button>

          {/* 末页按钮 */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(totalPages)}
            disabled={currentPage === totalPages || loading}
            className="h-9 px-2"
            title="末页"
          >
            <ChevronsRight className="w-4 h-4" />
          </Button>
        </div>

        {/* 跳转功能 */}
        <div className="flex items-center justify-center space-x-2 text-sm">
          <span className="text-muted-foreground whitespace-nowrap">跳转至</span>
          <Input
            type="number"
            value={jumpPage}
            onChange={(e) => setJumpPage(e.target.value)}
            onKeyPress={handleJumpKeyPress}
            placeholder="页码"
            min={1}
            max={totalPages}
            disabled={loading}
            className="w-16 h-8 text-center text-sm"
          />
          <span className="text-muted-foreground">页</span>
          <Button
            variant="outline"
            size="sm"
            onClick={handleJumpToPage}
            disabled={loading || !jumpPage}
            className="h-8"
          >
            跳转
          </Button>
        </div>
      </div>
    </div>
  );
}