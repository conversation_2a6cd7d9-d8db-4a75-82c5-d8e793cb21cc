'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Search } from 'lucide-react';
import SideFloatingNav from '@/components/layout/SideFloatingNav';
import CategoryFilter from '@/components/features/CategoryFilter';
import LinkCard from '@/components/features/LinkCard';
import SearchBar from '@/components/features/SearchBar';

interface Website {
  id: number;
  title: string;
  url: string;
  description: string;
  category_id: number;
  category_name: string;
  is_private: boolean;
  icon_media_id?: string;
  created_at: string;
  updated_at: string;
}

interface Category {
  id: number;
  name: string;
  order: number;
  is_private: boolean;
  links_count: number;
}

export default function SearchPage() {
  const [websites, setWebsites] = useState<Website[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [filteredWebsites, setFilteredWebsites] = useState<Website[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<any>(null);
  const searchParams = useSearchParams();

  useEffect(() => {
    const query = searchParams.get('q') || '';
    setSearchQuery(query);
    fetchData();
    checkUserStatus();
  }, [searchParams]);

  const checkUserStatus = async () => {
    try {
      const response = await fetch('/api/auth/me');
      if (response.ok) {
        const userData = await response.json();
        setUser(userData.data.user);
      }
    } catch (error) {
      console.error('Error checking user status:', error);
    }
  };

  const fetchData = async () => {
    try {
      setIsLoading(true);
      const [linksRes, categoriesRes] = await Promise.all([
        fetch('/api/links?limit=100'), // 获取更多数据
        fetch('/api/categories?limit=100') // 获取更多数据
      ]);

      const linksData = await linksRes.json();
      const categoriesData = await categoriesRes.json();

      // 修复数据解析 - links API返回 {data: {links: [...]}}, categories API返回 {data: {categories: [...]}}
      const links = linksData.success ? (linksData.data?.links || []) : [];
      const categories = categoriesData.success ? (categoriesData.data?.categories || []) : [];
      
      setLinks(Array.isArray(links) ? links : []);
      setCategories(Array.isArray(categories) ? categories : []);
    } catch (error) {
      console.error('Error fetching data:', error);
      setLinks([]);
      setCategories([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    let filtered = links;

    // 按分类过滤
    if (selectedCategory !== null) {
      filtered = filtered.filter(link => link.category_id === selectedCategory);
    }

    // 按搜索关键词过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(link => 
        (link.title && link.title.toLowerCase().includes(query)) ||
        (link.description && link.description.toLowerCase().includes(query)) ||
        (link.url && link.url.toLowerCase().includes(query))
      );
    }

    setFilteredLinks(filtered);
  }, [links, selectedCategory, searchQuery]);

  const handleCategorySelect = (categoryId: number | null) => {
    setSelectedCategory(categoryId);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    // 更新URL
    const url = new URL(window.location.href);
    if (query.trim()) {
      url.searchParams.set('q', query);
    } else {
      url.searchParams.delete('q');
    }
    window.history.pushState({}, '', url.toString());
  };

  // 统计数据
  const totalLinks = links.length;
  const totalCategories = categories.length;
  const recentLinks = links.filter(link => 
    new Date(link.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
  ).length;

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <SideFloatingNav user={user} />
        <div className="container mx-auto px-4 pt-20 max-w-4xl">
          <div className="text-center py-16">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">加载中...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <SideFloatingNav user={user} />
      
      {/* 简化的欢迎区域 */}
      <div className="relative pt-8 pb-12 px-4">
        <div className="container mx-auto max-w-7xl">
          {/* 简洁的标题区域 */}
          <div className="text-center mb-8">
            <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-3 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              搜索资源
            </h1>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
              在海量资源中找到您需要的内容
            </p>
          </div>
          
          {/* 快速统计 - 更紧凑 */}
          <div className="flex justify-center space-x-12 mb-8">
            <div className="text-center group">
              <div className="text-2xl font-bold text-primary group-hover:scale-110 transition-transform duration-300">{totalLinks}</div>
              <div className="text-sm text-muted-foreground">网站资源</div>
            </div>
            <div className="text-center group">
              <div className="text-2xl font-bold text-primary group-hover:scale-110 transition-transform duration-300">{totalCategories}</div>
              <div className="text-sm text-muted-foreground">分类目录</div>
            </div>
            <div className="text-center group">
              <div className="text-2xl font-bold text-primary group-hover:scale-110 transition-transform duration-300">{recentLinks}</div>
              <div className="text-sm text-muted-foreground">本周新增</div>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="container mx-auto px-4 max-w-7xl">
        {/* 搜索区域 - 更突出 */}
        <div className="mb-8">
          <div className="max-w-2xl mx-auto">
            <SearchBar onSearch={handleSearch} initialQuery={searchQuery} />
          </div>
        </div>
        
        {/* 分类筛选 */}
        <div className="mb-8">
          <CategoryFilter 
            categories={categories} 
            onCategorySelect={handleCategorySelect}
          />
        </div>

        {/* 资源展示区域 */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-foreground">
              {searchQuery ? `搜索 "${searchQuery}" 的结果` : '所有资源'}
            </h2>
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <span>共 {filteredLinks.length} 个资源</span>
            </div>
          </div>

          {/* 资源网格 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredLinks.map((link) => (
              <LinkCard
                key={link.id}
                link={link}
              />
            ))}
          </div>

          {/* 空状态 */}
          {filteredLinks.length === 0 && (
            <div className="text-center py-16">
              <div className="w-24 h-24 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-6 group hover:bg-muted transition-colors">
                <Search className="w-12 h-12 text-muted-foreground group-hover:scale-110 transition-transform duration-300" />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">
                {searchQuery ? '未找到相关资源' : '暂无资源'}
              </h3>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                {searchQuery 
                  ? '试试其他关键词或分类，或者为我们推荐优质资源'
                  : '成为第一个分享资源的人，帮助更多人发现优质内容'
                }
              </p>
              {user ? (
                <button className="px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors hover:scale-105 transform duration-200">
                  推荐网站
                </button>
              ) : (
                <button className="px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors hover:scale-105 transform duration-200">
                  立即注册推荐
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}