'use client';

import { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { Plus, Edit, Trash2, Search, RefreshCw } from 'lucide-react';
import { CrudModal, ConfirmDeleteModal } from '@/components/admin/CrudModal';
import { Pagination } from '@/components/admin/Pagination';
import { formatDateSafe } from '@/lib/utils/dateUtils';
import { useToast } from '@/contexts/ToastContext';
import { useUsers } from '@/hooks/useApiData';
import { AdminTableSkeleton } from '@/components/ui/SkeletonComponents';

interface User {
  id: number;
  username: string;
  email: string;
  role_id: number;
  role: string;
  action_count: number;
  created_at: string;
}

interface UsersData {
  users: User[];
  total: number;
  page: number;
  totalPages: number;
}

const userFormFields = [
  {
    name: 'username',
    label: '用户名',
    type: 'text' as const,
    placeholder: '请输入用户名',
    required: true
  },
  {
    name: 'email',
    label: '邮箱',
    type: 'email' as const,
    placeholder: '请输入邮箱地址',
    required: true
  },
  {
    name: 'password',
    label: '密码',
    type: 'password' as const,
    placeholder: '请输入密码（编辑时可留空）',
    required: false
  },
  {
    name: 'role',
    label: '角色',
    type: 'select' as const,
    required: true,
    options: [
      { label: '普通用户', value: 'user' },
      { label: '管理员', value: 'admin' },
      { label: '超级管理员', value: 'super_admin' }
    ]
  }
];

export default function UsersPageClient() {
  // 搜索和分页状态
  const [search, setSearch] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const { showSuccess, showError } = useToast();

  // 使用SWR进行数据获取
  const { users: usersData, isLoading, mutate: mutateUsers } = useUsers(
    currentPage,
    pageSize, 
    search
  );

  // 获取当前用户信息用于隔离（非超级管理员隐藏超级管理员用户；且禁止编辑/删除自己）
  const [currentUser, setCurrentUser] = useState<any>(null);
  useEffect(() => {
    (async () => {
      try {
        const res = await fetch('/api/auth/me');
        if (res.ok) {
          const json = await res.json();
          setCurrentUser(json?.data?.user || null);
        }
      } catch {}
    })();
  }, []);

  
  // 模态框状态
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [actionLoading, setActionLoading] = useState(false);


  // 搜索处理
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
  };

  // 创建用户
  const handleCreateUser = async (userData: Record<string, any>) => {
    setActionLoading(true);
    try {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData)
      });
      
      if (response.ok) {
        showSuccess('创建成功', `用户"${userData.username}"创建成功`);
        setIsCreateModalOpen(false);
        mutateUsers();
      } else {
        const error = await response.json();
        
        // 特殊处理重复用户错误
        if (error.error?.code === 'USER_ALREADY_EXISTS') {
          showError('用户已存在', `用户名"${userData.username}"或邮箱"${userData.email}"已被使用，请尝试使用不同的用户名或邮箱`);
          return; // 不抛出异常，让用户可以继续编辑
        }
        
        showError('创建失败', error.error?.message || '创建用户时发生错误');
      }
    } catch (error) {
      console.error('Create user error:', error);
      showError('创建失败', '网络错误或服务器异常，请稍后重试');
    } finally {
      setActionLoading(false);
    }
  };

  // 更新用户
  const handleUpdateUser = async (userData: Record<string, any>) => {
    if (!selectedUser) return;
    
    setActionLoading(true);
    try {
      const response = await fetch(`/api/users/${selectedUser.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData)
      });
      
      if (response.ok) {
        showSuccess('更新成功', `用户"${userData.username}"更新成功`);
        setIsEditModalOpen(false);
        setSelectedUser(null);
        mutateUsers();
      } else {
        const error = await response.json();
        
        // 特殊处理重复用户错误
        if (error.error?.code === 'USER_ALREADY_EXISTS') {
          showError('用户已存在', `用户名"${userData.username}"或邮箱"${userData.email}"已被其他用户使用，请尝试使用不同的用户名或邮箱`);
          return;
        }
        
        showError('更新失败', error.error?.message || '更新用户时发生错误');
      }
    } catch (error) {
      console.error('Update user error:', error);
      showError('更新失败', '网络错误或服务器异常，请稍后重试');
    } finally {
      setActionLoading(false);
    }
  };

  // 删除用户
  const handleDeleteUser = async () => {
    if (!selectedUser) return;
    
    setActionLoading(true);
    try {
      const response = await fetch(`/api/users/${selectedUser.id}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        showSuccess('删除成功', `用户"${selectedUser.username}"已删除`);
        setIsDeleteModalOpen(false);
        setSelectedUser(null);
        mutateUsers();
      } else {
        const error = await response.json();
        showError('删除失败', error.error?.message || '删除用户时发生错误');
      }
    } catch (error) {
      console.error('Delete user error:', error);
      showError('删除失败', '网络错误或服务器异常，请稍后重试');
    } finally {
      setActionLoading(false);
    }
  };

  // 编辑用户
  const handleEdit = (user: User) => {
    setSelectedUser(user);
    setIsEditModalOpen(true);
  };

  // 删除用户
  const handleDelete = (user: User) => {
    setSelectedUser(user);
    setIsDeleteModalOpen(true);
  };

  // 分页处理
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  // 页大小变化处理
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'super_admin': return 'destructive';
      case 'admin': return 'default';
      default: return 'secondary';
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'super_admin': return '超级管理员';
      case 'admin': return '管理员';
      default: return '普通用户';
    }
  };

  return (
    <div>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 sm:mb-8 space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-foreground mb-1 sm:mb-2">
            用户管理
          </h1>
          <p className="text-sm sm:text-base text-muted-foreground">
            管理系统用户和权限
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-0 sm:space-x-2">
          <Button variant="outline" onClick={() => mutateUsers()} className="w-full sm:w-auto">
            <RefreshCw className="w-4 h-4 mr-2" />
            刷新
          </Button>
          <Button onClick={() => setIsCreateModalOpen(true)} className="w-full sm:w-auto">
            <Plus className="w-4 h-4 mr-2" />
            新增用户
          </Button>
        </div>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg sm:text-xl">搜索用户</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-3 sm:gap-4">
            <div className="flex-1 relative">
              <Input
                type="text"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                placeholder="搜索用户名或邮箱..."
                className="pl-10"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            </div>
            <Button type="submit" size="default">
              <Search className="w-4 h-4 mr-2" />
              搜索
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>用户列表 ({usersData.total})</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <AdminTableSkeleton />
          ) : (
            <>
              {/* 桌面端表格 */}
              <div className="hidden lg:block overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-border">
                      <th className="text-left p-4">用户</th>
                      <th className="text-left p-4">邮箱</th>
                      <th className="text-left p-4">角色</th>
                      <th className="text-left p-4">操作数</th>
                      <th className="text-left p-4">注册时间</th>
                      <th className="text-left p-4">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {usersData.users.map((user) => (
                      <tr key={user.id} className="border-b border-border hover:bg-muted/50">
                        <td className="p-4">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-sm font-semibold">
                              {user.username.charAt(0).toUpperCase()}
                            </div>
                            <div>
                              <p className="font-medium text-foreground">{user.username}</p>
                              <p className="text-sm text-muted-foreground">ID: {user.id}</p>
                            </div>
                          </div>
                        </td>
                        <td className="p-4">
                          <p className="text-sm text-foreground">{user.email}</p>
                        </td>
                        <td className="p-4">
                          <Badge variant={getRoleBadgeVariant(user.role)}>
                            {getRoleLabel(user.role)}
                          </Badge>
                        </td>
                        <td className="p-4">
                          <p className="text-sm text-muted-foreground">{user.action_count}</p>
                        </td>
                        <td className="p-4">
                          <p className="text-sm text-muted-foreground">
                            {formatDateSafe(user.created_at)}
                          </p>
                        </td>
                        <td className="p-4">
                          <div className="flex items-center space-x-2">
                            {/* 非超级管理员不可编辑/删除超级管理员；且用户不能编辑/删除自己 */}
                            {!(currentUser && (currentUser.role !== 'super_admin' && user.role === 'super_admin')) && currentUser?.id !== user.id && (
                              <>
                                <Button variant="outline" size="sm" onClick={() => handleEdit(user)}>
                                  <Edit className="w-4 h-4" />
                                </Button>
                                <Button variant="outline" size="sm" onClick={() => handleDelete(user)}>
                                  <Trash2 className="w-4 h-4" />
                                </Button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* 移动端卡片 */}
              <div className="lg:hidden space-y-4">
                {usersData.users.map((user) => (
                  <Card key={user.id} className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-sm font-semibold">
                          {user.username.charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <p className="font-medium text-foreground">{user.username}</p>
                          <p className="text-xs text-muted-foreground">ID: {user.id}</p>
                        </div>
                      </div>
                      <Badge variant={getRoleBadgeVariant(user.role)}>
                        {getRoleLabel(user.role)}
                      </Badge>
                    </div>
                    
                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-muted-foreground">邮箱</span>
                        <span className="text-sm text-foreground">{user.email}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-muted-foreground">操作数</span>
                        <span className="text-sm text-foreground">{user.action_count}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-muted-foreground">注册时间</span>
                        <span className="text-sm text-foreground">
                          {formatDateSafe(user.created_at)}
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex space-x-2 pt-2 border-t border-border">
                      {/* 非超级管理员不可编辑/删除超级管理员；且用户不能编辑/删除自己 */}
                      {!(currentUser && (currentUser.role !== 'super_admin' && user.role === 'super_admin')) && currentUser?.id !== user.id && (
                        <>
                          <Button variant="outline" size="sm" onClick={() => handleEdit(user)} className="flex-1">
                            <Edit className="w-4 h-4 mr-1" />
                            编辑
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => handleDelete(user)} className="flex-1">
                            <Trash2 className="w-4 h-4 mr-1" />
                            删除
                          </Button>
                        </>
                      )}
                    </div>
                  </Card>
                ))}
              </div>

              {usersData.users.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">暂无用户数据</p>
                </div>
              )}

              {usersData.totalPages > 1 && (
                <div className="mt-6">
                  <Pagination
                    currentPage={usersData.page}
                    totalPages={usersData.totalPages}
                    total={usersData.total}
                    pageSize={pageSize}
                    onPageChange={handlePageChange}
                    onPageSizeChange={handlePageSizeChange}
                    loading={isLoading}
                  />
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* 创建用户模态框 */}
      <CrudModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="新增用户"
        fields={userFormFields}
        onSubmit={handleCreateUser}
        loading={actionLoading}
      />

      {/* 编辑用户模态框 */}
      <CrudModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedUser(null);
        }}
        title="编辑用户"
        fields={userFormFields.map(field => ({
          ...field,
          required: field.name === 'password' ? false : field.required
        }))}
        initialData={selectedUser ? {
          username: selectedUser.username,
          email: selectedUser.email,
          role: selectedUser.role,
          password: '' // 密码置空，表示不修改
        } : {}}
        onSubmit={handleUpdateUser}
        loading={actionLoading}
      />

      {/* 删除确认模态框 */}
      <ConfirmDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setSelectedUser(null);
        }}
        title="删除用户"
        message={`确定要删除用户 "${selectedUser?.username}" 吗？此操作不可恢复。`}
        onConfirm={handleDeleteUser}
        loading={actionLoading}
      />
    </div>
  );
}