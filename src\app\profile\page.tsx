'use client';

import { useState, useEffect } from 'react';
import { User, Mail, Calendar, Shield, Activity, Settings, Edit, Save, X } from 'lucide-react';
import SideFloatingNav from '@/components/layout/SideFloatingNav';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { formatDateSafe } from '@/lib/utils/dateUtils';
import { useToast } from '@/contexts/ToastContext';

interface UserProfile {
  id: number;
  username: string;
  email: string;
  role: string;
  avatar: string | null;
  actionCount: number;
  createdAt: string;
  updatedAt: string;
}

export default function ProfilePage() {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [formData, setFormData] = useState({
    username: '',
    email: ''
  });
  const { showSuccess, showError } = useToast();

  useEffect(() => {
    fetchUserProfile();
  }, []);

  const fetchUserProfile = async () => {
    try {
      const response = await fetch('/api/auth/me');
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data.user) {
          setUser(data.data.user);
          setFormData({
            username: data.data.user.username,
            email: data.data.user.email
          });
        }
      }
    } catch (error) {
      console.error('Failed to fetch user profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      const response = await fetch('/api/users/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          showSuccess('保存成功', '个人信息已成功更新');
          await fetchUserProfile();
          setEditing(false);
        } else {
          throw new Error(data.message || '更新失败');
        }
      } else {
        throw new Error('网络请求失败');
      }
    } catch (error) {
      console.error('Failed to update profile:', error);
      showError('保存失败', error instanceof Error ? error.message : '更新个人信息时发生错误，请重试');
    }
  };

  const handleCancel = () => {
    setFormData({
      username: user?.username || '',
      email: user?.email || ''
    });
    setEditing(false);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <div className="text-muted-foreground">正在加载个人信息...</div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-background">
        <SideFloatingNav user={null} />
        <div className="relative pt-6 pb-8 sm:pt-8 sm:pb-12 px-4">
          <div className="container mx-auto max-w-4xl">
            <div className="text-center py-16">
            <User className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-foreground mb-2">未登录</h1>
            <p className="text-muted-foreground mb-6">请先登录以查看个人中心</p>
            <Button onClick={() => window.location.href = '/auth/login'}>
              前往登录
            </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const handleLogout = async () => {
    try {
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
      });

      if (response.ok) {
        showSuccess('退出成功', '您已安全退出系统');
        // 短暂延迟后跳转，让用户看到成功提示
        setTimeout(() => {
          window.location.href = '/';
        }, 1000);
      } else {
        throw new Error('退出登录失败');
      }
    } catch (error) {
      console.error('Logout failed:', error);
      showError('退出失败', '退出登录时发生错误，请重试');
      // 即使失败也尝试重定向到首页
      setTimeout(() => {
        window.location.href = '/';
      }, 2000);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <SideFloatingNav user={user} />
      
      <div className="relative pt-6 pb-8 sm:pt-8 sm:pb-12 px-4">
        <div className="container mx-auto max-w-4xl">
          {/* 页面标题 */}
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold text-foreground mb-2">个人中心</h1>
            <p className="text-muted-foreground">管理您的个人信息和账户设置</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* 个人信息卡片 */}
            <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center space-x-2">
                    <User className="w-5 h-5" />
                    <span>基本信息</span>
                  </span>
                  {!editing ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditing(true)}
                    >
                      <Edit className="w-4 h-4 mr-1" />
                      编辑
                    </Button>
                  ) : (
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleCancel}
                      >
                        <X className="w-4 h-4 mr-1" />
                        取消
                      </Button>
                      <Button
                        size="sm"
                        onClick={handleSave}
                      >
                        <Save className="w-4 h-4 mr-1" />
                        保存
                      </Button>
                    </div>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* 头像区域 */}
                <div className="flex items-center space-x-4">
                  <div className="w-20 h-20 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-2xl font-bold">
                    {user.username.charAt(0).toUpperCase()}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-foreground">{user.username}</h3>
                    <Badge variant={user.role === 'super_admin' ? 'destructive' : user.role === 'admin' ? 'default' : 'secondary'}>
                      {user.role === 'super_admin' ? '超级管理员' : user.role === 'admin' ? '管理员' : '普通用户'}
                    </Badge>
                  </div>
                </div>

                {/* 用户名 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">用户名</label>
                  {editing ? (
                    <Input
                      value={formData.username}
                      onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                      placeholder="请输入用户名"
                    />
                  ) : (
                    <div className="px-3 py-2 bg-muted/50 rounded-lg">
                      {user.username}
                    </div>
                  )}
                </div>

                {/* 邮箱 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">邮箱地址</label>
                  {editing ? (
                    <Input
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                      placeholder="请输入邮箱地址"
                    />
                  ) : (
                    <div className="px-3 py-2 bg-muted/50 rounded-lg">
                      {user.email}
                    </div>
                  )}
                </div>

                {/* 注册时间 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">注册时间</label>
                  <div className="px-3 py-2 bg-muted/50 rounded-lg flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-muted-foreground" />
                    <span>{formatDateSafe(user.createdAt)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 侧边栏信息 */}
          <div className="space-y-6">
            {/* 账户统计 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Activity className="w-5 h-5" />
                  <span>账户统计</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">操作次数</span>
                  <span className="font-semibold text-foreground">{user.actionCount}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">账户状态</span>
                  <Badge variant="secondary">正常</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">最后更新</span>
                  <span className="text-sm text-muted-foreground">
                    {formatDateSafe(user.updatedAt)}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* 快速操作 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Settings className="w-5 h-5" />
                  <span>快速操作</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <Mail className="w-4 h-4 mr-2" />
                  修改密码
                </Button>
                
                {(user.role === 'admin' || user.role === 'super_admin') && (
                  <Button 
                    variant="outline" 
                    className="w-full justify-start"
                    onClick={() => window.location.href = '/admin/dashboard'}
                  >
                    <Shield className="w-4 h-4 mr-2" />
                    管理后台
                  </Button>
                )}
                
                <Button 
                  variant="outline" 
                  className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950/20"
                  onClick={handleLogout}
                >
                  <User className="w-4 h-4 mr-2" />
                  退出登录
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
        </div>
      </div>
    </div>
  );
}