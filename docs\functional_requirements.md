# 功能需求文档

## 项目概述

### 项目名称
资源导航网站管理系统

### 项目描述
开发一个功能完整的资源导航网站，提供各类网站资源的分类展示和管理功能。系统采用三级权限控制，支持游客浏览、管理员资源管理和超级管理员系统控制。

### 技术架构
- **前端**：Next.js 15 + TypeScript + Tailwind CSS + Framer Motion
- **后端**：Next.js API Routes + MySQL 5.7 + mysql2
- **认证**：bcrypt + session + JWT
- **文件处理**：multer + sharp
- **数据验证**：zod

## 用户系统需求

### 用户角色定义

#### 1. 游客（Guest）
- **权限范围**：
  - 浏览公开资源
  - 访问公开分类
  - 查看资源详情
  - 搜索公开资源

#### 2. 普通管理员（Admin）
- **权限范围**：
  - 拥有游客所有权限
  - 管理网站资源（增删改查）
  - 管理分类（增删改查）
  - 上传和管理媒体文件
  - 设置资源隐私状态
  - 查看个人操作日志

#### 3. 超级管理员（Super Admin）
- **权限范围**：
  - 拥有普通管理员所有权限
  - 管理所有管理员账号
  - 系统权限设置
  - 查看所有操作日志
  - 系统配置管理
  - 数据备份和恢复

### 用户功能需求

#### 1. 注册登录功能
- **用户注册**：
  - 用户名唯一性验证
  - 邮箱验证
  - 密码强度要求
  - 默认角色分配

- **用户登录**：
  - 用户名/邮箱登录
  - 密码验证
  - 会话管理
  - 记住登录状态
  - 登录失败限制

#### 2. 个人信息管理
- **信息修改**：
  - 用户名修改
  - 邮箱修改
  - 密码修改
  - 头像上传

- **偏好设置**：
  - 界面主题设置
  - 显示偏好设置
  - 通知设置

#### 3. 权限控制
- **访问控制**：
  - 基于角色的权限检查
  - 路由级别权限控制
  - API级别权限控制
  - 组件级别权限控制

- **权限验证**：
  - 前端权限检查
  - 后端权限验证
  - 权限越界处理
  - 权限变更通知

## 资源管理系统需求

### 分类管理

#### 1. 分类功能
- **多级分类**：
  - 支持无限级分类
  - 分类层级关系维护
  - 分类路径管理
  - 分类深度限制

- **分类属性**：
  - 分类名称（必填）
  - 分类图标
  - 排序权重
  - 隐私设置
  - 创建者信息

#### 2. 分类操作
- **基本操作**：
  - 创建分类
  - 编辑分类信息
  - 删除分类（级联处理）
  - 查看分类详情

- **批量操作**：
  - 批量删除
  - 批量修改隐私设置
  - 批量修改排序
  - 批量移动分类

#### 3. 分类排序
- **排序机制**：
  - 手动拖拽排序
  - 数值权重排序
  - 自动排序算法
  - 排序冲突处理

- **排序展示**：
  - 影响首页展示顺序
  - 分类列表排序
  - 实时排序更新
  - 排序历史记录

### 网站管理

#### 1. 网站功能
- **网站属性**：
  - 网站标题（必填）
  - 网站URL（必填，格式验证）
  - 网站描述
  - 所属分类（必填）
  - 创建者信息
  - 隐私设置
  - 网站图标

#### 2. 网站操作
- **基本操作**：
  - 添加网站
  - 编辑网站信息
  - 删除网站
  - 查看网站详情
  - 访问网站

- **批量操作**：
  - 批量导入（CSV/JSON）
  - 批量导出
  - 批量删除
  - 批量修改分类
  - 批量修改隐私设置

#### 3. 图标管理
- **图标生成**：
  - 自动生成算法
  - 首字母提取
  - 背景色生成（确保不重复）
  - SVG格式输出
  - 图标缓存机制

- **图标上传**：
  - 图片文件上传
  - 图片格式验证
  - 图片尺寸调整
  - 图片压缩优化
  - 图片存储管理

#### 4. 快捷添加功能
- **快速添加**：
  - 导航页直接添加
  - URL自动解析
  - 标题自动获取
  - 描述自动生成
  - 一键保存功能

### 数据权限

#### 1. 公开数据
- **访问权限**：
  - 所有用户可见
  - 游客可访问
  - 搜索引擎可索引
  - 公开分类展示

#### 2. 隐私数据
- **访问权限**：
  - 仅创建者可见
  - 管理员可见
  - 超级管理员可见
  - 不在公开搜索中显示

## 超级管理员功能

### 管理员管理

#### 1. 管理员账号管理
- **账号操作**：
  - 创建管理员账号
  - 编辑管理员信息
  - 删除管理员账号
  - 重置管理员密码
  - 管理员状态管理

#### 2. 权限设置
- **权限分配**：
  - 角色分配和修改
  - 细粒度权限设置
  - 权限模板管理
  - 权限继承设置
  - 临时权限授权

#### 3. 管理员监控
- **活动监控**：
  - 在线状态监控
  - 登录记录查看
  - 操作频率统计
  - 异常行为检测
  - 管理员工作量统计

### 日志记录系统

#### 1. 操作日志
- **日志内容**：
  - 用户登录/登出
  - 资源增删改查
  - 权限变更
  - 系统配置修改
  - 文件上传/删除

#### 2. 日志功能
- **记录功能**：
  - 自动记录操作
  - 操作时间戳
  - 操作用户信息
  - 操作对象信息
  - 操作结果状态

#### 3. 日志查询
- **查询功能**：
  - 按用户查询
  - 按时间范围查询
  - 按操作类型查询
  - 按操作对象查询
  - 组合条件查询

#### 4. 日志管理
- **管理功能**：
  - 日志导出
  - 日志备份
  - 日志清理
  - 日志分析
  - 安全审计

## 前端展示功能

### 首页设计

#### 1. 瀑布流布局
- 流畅的视觉体验
- 移动端适配

### 分类导航

#### 1. 分类展示
- **展示形式**：
  - 顶部导航栏
  - 侧边分类树
  - 面包屑导航
  - 分类标签云
  - 搜索建议

#### 2. 分类筛选
- **筛选功能**：
  - 单分类筛选
  - 多分类筛选
  - 层级筛选
  - 快速切换
  - 筛选历史

### 搜索功能

#### 1. 搜索功能
- **搜索类型**：
  - 全文搜索
  - 标题搜索
  - 描述搜索
  - URL搜索
  - 分类搜索

#### 2. 搜索体验
- **用户体验**：
  - 实时搜索建议
  - 搜索历史记录
  - 热门搜索推荐
  - 搜索结果高亮
  - 搜索结果分页

### 响应式设计

#### 1. 设备适配
- **适配设备**：
  - 桌面端（>1200px）
  - 平板端（768px-1200px）
  - 移动端（<768px）
  - 不同屏幕比例
  - 横竖屏适配

#### 2. 响应式特性
- **响应式功能**：
  - 流式布局
  - 弹性图片
  - 触摸友好交互
  - 设备专属功能
  - 性能优化

### 动效和交互

#### 1. 页面动效
- **动效类型**：
  - 页面切换动画
  - 元素进入动画
  - 加载状态动画
  - 错误提示动画
  - 成功反馈动画

#### 2. 交互效果
- **交互反馈**：
  - 按钮点击反馈
  - 卡片悬停效果
  - 表单验证反馈
  - 拖拽操作反馈
  - 进度指示器

#### 3. 高级效果
- **3D效果**：
  - 卡片3D翻转
  - 视差滚动
  - 3D导航效果
  - 空间深度感
  - 立体动画

### 性能优化

#### 1. 加载优化
- **优化策略**：
  - 懒加载机制
  - 预加载关键资源
  - 代码分割
  - 资源压缩
  - CDN加速

#### 2. 运行优化
- **优化措施**：
  - 虚拟滚动
  - 防抖节流
  - 内存管理
  - 渲染优化
  - 缓存策略

## 管理后台功能

### 后台结构

#### 1. 管理布局
- **布局设计**：
  - 侧边栏导航
  - 顶部状态栏
  - 主内容区域
  - 面包屑导航
  - 快捷操作栏

#### 2. 管理模块
- **功能模块**：
  - 仪表盘
  - 用户管理
  - 分类管理
  - 网站管理
  - 权限管理
  - 日志管理
  - 系统设置

### 数据管理

#### 1. 数据表格
- **表格功能**：
  - 数据展示
  - 排序功能
  - 分页功能
  - 搜索过滤
  - 批量操作

#### 2. 表单处理
- **表单功能**：
  - 数据验证
  - 实时验证
  - 错误提示
  - 自动保存
  - 表单重置

### 系统设置

#### 1. 基础设置
- **设置项**：
  - 网站基本信息
  - 默认配置
  - 邮件设置
  - 文件上传设置
  - 安全设置

#### 2. 高级设置
- **高级功能**：
  - 数据备份
  - 系统监控
  - 性能优化
  - 第三方集成
  - API设置

## 技术实现需求

### 数据库要求

#### 1. 数据库配置
- **基础信息**：
  - 类型：MySQL 5.7
  - 连接池配置
  - 事务支持

#### 2. 数据表结构
- **核心表**：
  - users（用户表）
  - roles（角色表）
  - permissions（权限表）
  - categories（分类表）
  - links（网站表）
  - media（媒体文件表）
  - sessions（会话表）
  - activity_logs（日志表）

