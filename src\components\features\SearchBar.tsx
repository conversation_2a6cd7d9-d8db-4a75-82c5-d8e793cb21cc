'use client';

import { useState, useEffect } from 'react';
import { Search, X } from 'lucide-react';

interface SearchBarProps {
  onSearch?: (query: string) => void;
  initialQuery?: string;
}

export default function SearchBar({ onSearch, initialQuery = '' }: SearchBarProps) {
  const [query, setQuery] = useState(initialQuery);
  const [isFocused, setIsFocused] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);

  useEffect(() => {
    setQuery(initialQuery);
  }, [initialQuery]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (onSearch && query.trim()) {
      onSearch(query.trim());
      setShowSuggestions(false);
    }
  };

  const clearSearch = () => {
    setQuery('');
    if (onSearch) {
      onSearch('');
    }
    setShowSuggestions(false);
  };

  const handleInputChange = (value: string) => {
    setQuery(value);
    setShowSuggestions(value.length > 0);
  };

  // 模拟搜索建议
  const suggestions = query.length > 0 ? [
    `${query} 教程`,
    `${query} 工具`,
    `${query} 资源`,
    `${query} 网站`
  ] : [];

  return (
    <div className="relative">
      <form onSubmit={handleSubmit} className="relative">
        <div className={`absolute inset-y-0 left-0 flex items-center pl-3 sm:pl-4 pointer-events-none transition-colors ${isFocused ? 'text-primary' : 'text-muted-foreground'}`}>
          <Search className="w-4 h-4 sm:w-5 sm:h-5" />
        </div>
        
        <input
          type="text"
          value={query}
          onChange={(e) => handleInputChange(e.target.value)}
          onFocus={() => {
            setIsFocused(true);
            if (query.length > 0) {
              setShowSuggestions(true);
            }
          }}
          onBlur={() => {
            setIsFocused(false);
            // 延迟隐藏建议，以便用户可以点击
            setTimeout(() => setShowSuggestions(false), 200);
          }}
          placeholder="搜索网站、描述或URL..."
          className={`search-modern ${query ? 'pr-20' : 'pr-16'} pl-14 ${
            isFocused ? 'shadow-lg scale-105' : ''
          }`}
        />
        
        {query && (
          <button
            type="button"
            onClick={clearSearch}
            className="absolute inset-y-0 right-8 sm:right-12 flex items-center pr-1 sm:pr-2 text-muted-foreground hover:text-foreground transition-colors"
          >
            <X className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
          </button>
        )}
        
        <button
          type="submit"
          className="absolute inset-y-0 right-0 flex items-center pr-3 sm:pr-4 text-muted-foreground hover:text-primary transition-colors"
        >
          <Search className="w-4 h-4 sm:w-5 sm:h-5" />
        </button>
      </form>
      
      {/* 现代化搜索建议 */}
      {showSuggestions && suggestions.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-4 glass-effect rounded-2xl shadow-2xl z-10 animate-fade-in-scale">
          <div className="p-4">
            <div className="text-xs text-muted-foreground mb-3 px-2">搜索建议</div>
            {suggestions.map((suggestion, index) => (
              <button
                key={index}
                type="button"
                onClick={() => {
                  setQuery(suggestion);
                  setShowSuggestions(false);
                  if (onSearch) {
                    onSearch(suggestion);
                  }
                }}
                className="w-full text-left px-4 py-3 text-sm text-foreground hover:bg-primary/10 rounded-xl transition-all duration-200 flex items-center gap-3 group"
              >
                <div className="w-8 h-8 bg-muted/50 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                  <Search className="w-4 h-4 text-muted-foreground group-hover:text-primary" />
                </div>
                <span className="group-hover:text-primary transition-colors">{suggestion}</span>
              </button>
            ))}
          </div>
        </div>
      )}
      
      {/* 快速搜索提示 */}
      {query && !showSuggestions && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-background border border-border rounded-lg shadow-lg z-10">
          <div className="p-4">
            <p className="text-sm text-muted-foreground mb-2">
              搜索 "{query}"
            </p>
            <button
              type="submit"
              className="w-full py-2 px-4 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            >
              搜索所有结果
            </button>
          </div>
        </div>
      )}
    </div>
  );
}