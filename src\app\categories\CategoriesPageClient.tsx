'use client';

import { useState, useEffect } from 'react';
import { <PERSON>olderO<PERSON>, Star, Clock, TrendingUp, Users, Search } from 'lucide-react';
import SideFloatingNav from '@/components/layout/SideFloatingNav';
import CategoryFilter from '@/components/features/CategoryFilter';
import LinkCard from '@/components/features/LinkCard';
import SearchBar from '@/components/features/SearchBar';

interface Website {
  id: number;
  title: string;
  url: string;
  description: string;
  category_id: number;
  category_name: string;
  is_private: boolean;
  icon_media_id?: string;
  created_at: string;
  updated_at: string;
}

interface Category {
  id: number;
  name: string;
  order: number;
  is_private: boolean;
  links_count: number;
}

interface CategoriesPageProps {
  initialCategories: Category[];
  initialLinks: Website[];
}

export default function CategoriesPageClient({ initialCategories, initialLinks }: CategoriesPageProps) {
  const [categories, setCategories] = useState<Category[]>(initialCategories);
  const [websites, setWebsites] = useState<Website[]>(initialLinks);
  const [filteredWebsites, setFilteredWebsites] = useState<Website[]>(initialLinks);
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [user, setUser] = useState<any>(null);

  // 检查用户登录状态
  useEffect(() => {
    checkUserStatus();
  }, []);

  const checkUserStatus = async () => {
    try {
      const response = await fetch('/api/auth/me');
      if (response.ok) {
        const userData = await response.json();
        setUser(userData.data.user);
      }
    } catch (error) {
      console.error('Error checking user status:', error);
    }
  };

  // 过滤网站
  useEffect(() => {
    let filtered = websites;

    // 按分类过滤
    if (selectedCategory !== null) {
      filtered = filtered.filter(website => website.category_id === selectedCategory);
    }

    // 按搜索关键词过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(website => 
        (website.title && website.title.toLowerCase().includes(query)) ||
        (website.description && website.description.toLowerCase().includes(query)) ||
        (website.url && website.url.toLowerCase().includes(query))
      );
    }

    setFilteredWebsites(filtered);
  }, [websites, selectedCategory, searchQuery]);

  const handleCategorySelect = (categoryId: number | null) => {
    setSelectedCategory(categoryId);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  // 统计数据
  const totalWebsites = initialLinks.length;
  const totalCategories = initialCategories.length;
  const recentWebsites = initialLinks.filter(website => 
    new Date(website.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
  ).length;

  return (
    <div className="min-h-screen bg-background">
      <SideFloatingNav user={user} />
      
      {/* 简化的欢迎区域 */}
      <div className="relative pt-8 pb-12 px-4">
        <div className="container mx-auto max-w-7xl">
          {/* 简洁的标题区域 */}
          <div className="text-center mb-8">
            <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-3 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              分类浏览
            </h1>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
              按分类探索优质网站资源
            </p>
          </div>
          
          {/* 快速统计 - 更紧凑 */}
          <div className="flex justify-center space-x-12 mb-8">
            <div className="text-center group">
              <div className="text-2xl font-bold text-primary group-hover:scale-110 transition-transform duration-300">{totalCategories}</div>
              <div className="text-sm text-muted-foreground">分类目录</div>
            </div>
            <div className="text-center group">
              <div className="text-2xl font-bold text-primary group-hover:scale-110 transition-transform duration-300">{totalWebsites}</div>
              <div className="text-sm text-muted-foreground">网站资源</div>
            </div>
            <div className="text-center group">
              <div className="text-2xl font-bold text-primary group-hover:scale-110 transition-transform duration-300">{recentWebsites}</div>
              <div className="text-sm text-muted-foreground">本周新增</div>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="container mx-auto px-4 max-w-7xl">
        {/* 搜索区域 */}
        <div className="mb-8">
          <div className="max-w-2xl mx-auto">
            <SearchBar onSearch={handleSearch} />
          </div>
        </div>
        
        {/* 分类筛选 */}
        <div className="mb-8">
          <CategoryFilter 
            categories={categories} 
            onCategorySelect={handleCategorySelect}
          />
        </div>

        {/* 分类卡片展示 */}
        {!selectedCategory && !searchQuery && (
          <div className="mb-12">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-foreground">所有分类</h2>
              <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                <div className="flex items-center space-x-1">
                  <FolderOpen className="w-4 h-4" />
                  <span>分类目录</span>
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => handleCategorySelect(category.id)}
                  className="group p-6 bg-card/50 border border-border/50 rounded-xl hover:border-primary/50 hover:bg-card transition-all duration-300 text-left hover:scale-105 transform"
                >
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-foreground group-hover:text-primary transition-colors">
                      {category.name}
                    </h3>
                    <div className="text-sm text-muted-foreground">
                      {category.links_count} 个
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    点击查看该分类下的所有资源
                  </p>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* 资源展示区域 */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-foreground">
              {selectedCategory 
                ? `${categories.find(c => c.id === selectedCategory)?.name} 相关资源`
                : searchQuery 
                ? `搜索 "${searchQuery}" 的结果`
                : '最新资源'
              }
            </h2>
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <span>共 {filteredWebsites.length} 个资源</span>
            </div>
          </div>

          {/* 资源网格 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {(selectedCategory || searchQuery ? filteredWebsites : filteredWebsites.slice(0, 12)).map((website) => (
              <LinkCard
                key={website.id}
                link={website}
              />
            ))}
          </div>

          {/* 空状态 */}
          {filteredWebsites.length === 0 && (
            <div className="text-center py-16">
              <div className="w-24 h-24 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-6 group hover:bg-muted transition-colors">
                <Search className="w-12 h-12 text-muted-foreground group-hover:scale-110 transition-transform duration-300" />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">
                {selectedCategory || searchQuery ? '未找到相关资源' : '暂无资源'}
              </h3>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                {selectedCategory || searchQuery 
                  ? '试试其他关键词或分类，或者为我们推荐优质资源'
                  : '成为第一个分享资源的人，帮助更多人发现优质内容'
                }
              </p>
              {user ? (
                <button className="px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors hover:scale-105 transform duration-200">
                  推荐网站
                </button>
              ) : (
                <button className="px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors hover:scale-105 transform duration-200">
                  立即注册推荐
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}