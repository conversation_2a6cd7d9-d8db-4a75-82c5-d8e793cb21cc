'use client';

import { useState, useEffect } from 'react';
import { Home, Search, FolderOpen, User, LogIn, Settings, Menu, X } from 'lucide-react';
import Link from 'next/link';
import { useToast } from '@/contexts/ToastContext';

interface SideFloatingNavProps {
  user?: {
    id: number;
    username: string;
    email: string;
    role: string;
  } | null;
}

export default function SideFloatingNav({ user }: SideFloatingNavProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const { showSuccess, showError } = useToast();

  const handleLogout = async () => {
    try {
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
      });

      if (response.ok) {
        showSuccess('退出成功', '您已安全退出系统');
        // 短暂延迟后跳转，让用户看到成功提示
        setTimeout(() => {
          window.location.href = '/';
        }, 1000);
      } else {
        throw new Error('退出登录失败');
      }
    } catch (error) {
      console.error('Logout failed:', error);
      showError('退出失败', '退出登录时发生错误，请重试');
      // 即使失败也尝试重定向到首页
      setTimeout(() => {
        window.location.href = '/';
      }, 2000);
    }
  };

  return (
    <>
      {/* 触发按钮 - 固定在左侧 */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={`fixed left-6 top-6 z-50 w-12 h-12 rounded-full bg-background/90 backdrop-blur-lg border border-border/50 shadow-xl flex items-center justify-center transition-all duration-300 hover:scale-110 hover:shadow-2xl ${
          isExpanded ? 'rotate-180' : ''
        }`}
      >
        {isExpanded ? (
          <X className="w-5 h-5 text-foreground" />
        ) : (
          <Menu className="w-5 h-5 text-foreground" />
        )}
      </button>

      {/* 侧边栏 */}
      <div
        className={`fixed left-0 top-0 h-screen w-72 bg-background/95 backdrop-blur-xl border-r border-border/30 shadow-2xl transform transition-all duration-500 ease-out z-40 ${
          isExpanded ? 'translate-x-0' : '-translate-x-full'
        }`}
        style={{
          background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
          backdropFilter: 'blur(20px)',
        }}
      >
        {/* 品牌区域 */}
        <div className="p-6 border-b border-border/20">
          <Link 
            href="/" 
            className="flex items-center space-x-3 group"
            onClick={() => setIsExpanded(false)}
          >
            <div className="w-10 h-10 bg-gradient-to-br from-primary/20 to-primary/10 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <Home className="w-5 h-5 text-primary" />
            </div>
            <div>
              <h1 className="text-lg font-bold text-foreground">资源导航</h1>
              <p className="text-xs text-muted-foreground">发现优质资源</p>
            </div>
          </Link>
        </div>

        {/* 主导航 */}
        <div className="p-6 space-y-3">
          <Link
            href="/"
            className="flex items-center space-x-3 px-4 py-3 rounded-xl text-foreground hover:bg-muted/50 transition-all duration-300 group hover:translate-x-2"
            onClick={() => setIsExpanded(false)}
          >
            <Home className="w-5 h-5 group-hover:scale-110 transition-transform" />
            <span className="font-medium">首页</span>
          </Link>

          <Link
            href="/categories"
            className="flex items-center space-x-3 px-4 py-3 rounded-xl text-foreground hover:bg-muted/50 transition-all duration-300 group hover:translate-x-2"
            onClick={() => setIsExpanded(false)}
          >
            <FolderOpen className="w-5 h-5 group-hover:scale-110 transition-transform" />
            <span className="font-medium">分类浏览</span>
          </Link>

          <Link
            href="/search"
            className="flex items-center space-x-3 px-4 py-3 rounded-xl text-foreground hover:bg-muted/50 transition-all duration-300 group hover:translate-x-2"
            onClick={() => setIsExpanded(false)}
          >
            <Search className="w-5 h-5 group-hover:scale-110 transition-transform" />
            <span className="font-medium">搜索资源</span>
          </Link>
        </div>

        {/* 用户区域 */}
        <div className="absolute bottom-0 left-0 right-0 p-6 border-t border-border/20">
          {user ? (
            <div className="space-y-3">
              <div className="flex items-center space-x-3 px-4 py-3 bg-muted/30 rounded-xl">
                <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-primary-foreground" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-foreground truncate">{user.username}</p>
                  <p className="text-xs text-muted-foreground">{user.role}</p>
                </div>
              </div>

              <Link
                href="/profile"
                className="flex items-center space-x-3 px-4 py-2 rounded-xl text-sm text-foreground hover:bg-muted/50 transition-all duration-300 group hover:translate-x-2"
                onClick={() => setIsExpanded(false)}
              >
                <User className="w-4 h-4 group-hover:scale-110 transition-transform" />
                <span>个人中心</span>
              </Link>

              {(user.role === 'admin' || user.role === 'super_admin') && (
                <Link
                  href="/admin/dashboard"
                  className="flex items-center space-x-3 px-4 py-2 rounded-xl text-sm text-foreground hover:bg-muted/50 transition-all duration-300 group hover:translate-x-2"
                  onClick={() => setIsExpanded(false)}
                >
                  <Settings className="w-4 h-4 group-hover:scale-110 transition-transform" />
                  <span>管理后台</span>
                </Link>
              )}

              <button
                className="flex items-center space-x-3 px-4 py-2 rounded-xl text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-950/20 transition-all duration-300 group hover:translate-x-2 w-full text-left"
                onClick={() => {
                  setIsExpanded(false);
                  handleLogout();
                }}
              >
                <LogIn className="w-4 h-4 group-hover:scale-110 transition-transform rotate-180" />
                <span>退出登录</span>
              </button>
            </div>
          ) : (
            <div className="space-y-3">
              <Link
                href="/auth/login"
                className="flex items-center justify-center space-x-2 w-full px-4 py-3 bg-primary text-primary-foreground rounded-xl hover:bg-primary/90 transition-all duration-300 hover:scale-105"
                onClick={() => setIsExpanded(false)}
              >
                <LogIn className="w-4 h-4" />
                <span className="font-medium">登录</span>
              </Link>

              <Link
                href="/auth/register"
                className="flex items-center justify-center space-x-2 w-full px-4 py-3 border border-border rounded-xl hover:bg-muted/50 transition-all duration-300 hover:scale-105"
                onClick={() => setIsExpanded(false)}
              >
                <User className="w-4 h-4" />
                <span className="font-medium">注册</span>
              </Link>

              <Link
                href="/admin/login"
                className="flex items-center justify-center space-x-2 w-full px-4 py-2 text-sm text-muted-foreground hover:text-primary transition-colors"
                onClick={() => setIsExpanded(false)}
              >
                <Settings className="w-3 h-3" />
                <span>管理员入口</span>
              </Link>
            </div>
          )}
        </div>
      </div>

      {/* 遮罩层 */}
      {isExpanded && (
        <div
          className="fixed inset-0 bg-black/20 backdrop-blur-sm z-30 transition-opacity duration-300"
          onClick={() => setIsExpanded(false)}
        />
      )}
    </>
  );
}