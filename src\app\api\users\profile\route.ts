import { NextRequest, NextResponse } from 'next/server';
import { query, queryOne } from '@/lib/database';
import { createApiResponse, createApiError, withAuth } from '@/lib/utils';
import { hashPassword } from '@/lib/utils/server';

// 获取当前用户个人资料
export const GET = withAuth(async (request: NextRequest) => {
  try {
    const user = (request as any).user;
    
    const userData = await queryOne(`
      SELECT 
        u.id,
        u.username,
        u.email,
        u.role_id,
        r.name as role,
        u.avatar_media_id,
        u.created_at,
        u.updated_at,
        (SELECT COUNT(*) FROM activity_logs WHERE user_id = u.id) as action_count
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.id
      WHERE u.id = ?
    `, [user.id]);

    if (!userData) {
      return createApiError('USER_NOT_FOUND', 'User not found', null, 404);
    }

    return createApiResponse(true, userData);
    
  } catch (error) {
    console.error('Get profile error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'Failed to fetch profile', error, 500);
  }
});

// 更新当前用户个人资料
export const PUT = withAuth(async (request: NextRequest) => {
  try {
    const user = (request as any).user;
    const body = await request.json();
    
    const { username, email, currentPassword, newPassword, avatar } = body;
    
    // 验证必填字段
    if (!username || !email) {
      return createApiError('VALIDATION_ERROR', 'Username and email are required', null, 400);
    }

    // 检查用户名和邮箱是否已被其他用户使用
    const existingUser = await queryOne(
      'SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?',
      [username, email, user.id]
    );
    
    if (existingUser) {
      return createApiError('USER_ALREADY_EXISTS', 'Username or email already exists', null, 400);
    }

    // 构建更新查询
    const updateFields: string[] = [];
    const updateParams: any[] = [];

    updateFields.push('username = ?', 'email = ?', 'updated_at = NOW()');
    updateParams.push(username, email);

    // 如果要更新头像
    if (avatar !== undefined) {
      updateFields.push('avatar_media_id = ?');
      updateParams.push(avatar);
    }

    // 如果要修改密码
    if (newPassword) {
      if (!currentPassword) {
        return createApiError('VALIDATION_ERROR', 'Current password is required to change password', null, 400);
      }

      // 验证当前密码
      const currentUser = await queryOne('SELECT password FROM users WHERE id = ?', [user.id]);
      if (!currentUser) {
        return createApiError('USER_NOT_FOUND', 'User not found', null, 404);
      }

      const bcrypt = await import('bcrypt');
      const isValidPassword = await bcrypt.compare(currentPassword, currentUser.password);
      if (!isValidPassword) {
        return createApiError('INVALID_PASSWORD', 'Current password is incorrect', null, 400);
      }

      // 添加新密码到更新字段
      const hashedNewPassword = await hashPassword(newPassword);
      updateFields.push('password = ?');
      updateParams.push(hashedNewPassword);
    }

    // 添加用户ID到参数末尾
    updateParams.push(user.id);

    // 执行更新
    await query(
      `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`,
      updateParams
    );

    // 获取更新后的用户信息
    const updatedUser = await queryOne(`
      SELECT 
        u.id,
        u.username,
        u.email,
        u.role_id,
        r.name as role,
        u.avatar_media_id,
        u.created_at,
        u.updated_at,
        (SELECT COUNT(*) FROM activity_logs WHERE user_id = u.id) as action_count
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.id
      WHERE u.id = ?
    `, [user.id]);

    // 记录操作日志
    await query(
      'INSERT INTO activity_logs (user_id, action, target_type, target_id, created_at) VALUES (?, ?, ?, ?, NOW())',
      [user.id, 'profile_update', 'user', user.id]
    );

    return createApiResponse(true, updatedUser, 'Profile updated successfully');
    
  } catch (error) {
    console.error('Update profile error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'Failed to update profile', error, 500);
  }
});