'use client';

import { useEffect, useState } from 'react';
import { X, CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-react';
import { ToastMessage } from '@/types/toast';

interface ToastProps {
  toast: ToastMessage;
  onClose: (id: string) => void;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
}

const iconMap = {
  success: CheckCircle,
  error: AlertCircle,
  warning: AlertTriangle,
  info: Info,
};

const colorMap = {
  success: {
    bg: 'bg-emerald-500/10 dark:bg-emerald-400/10',
    border: 'border-emerald-200/30 dark:border-emerald-400/30',
    icon: 'text-emerald-600 dark:text-emerald-400',
    progress: 'bg-emerald-500',
  },
  error: {
    bg: 'bg-red-500/10 dark:bg-red-400/10',
    border: 'border-red-200/30 dark:border-red-400/30',
    icon: 'text-red-600 dark:text-red-400',
    progress: 'bg-red-500',
  },
  warning: {
    bg: 'bg-amber-500/10 dark:bg-amber-400/10',
    border: 'border-amber-200/30 dark:border-amber-400/30',
    icon: 'text-amber-600 dark:text-amber-400',
    progress: 'bg-amber-500',
  },
  info: {
    bg: 'bg-blue-500/10 dark:bg-blue-400/10',
    border: 'border-blue-200/30 dark:border-blue-400/30',
    icon: 'text-blue-600 dark:text-blue-400',
    progress: 'bg-blue-500',
  },
};

export default function Toast({ toast, onClose, onMouseEnter, onMouseLeave }: ToastProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [progress, setProgress] = useState(100);
  const [isPaused, setIsPaused] = useState(false);

  const Icon = iconMap[toast.type];
  const colors = colorMap[toast.type];
  const duration = toast.duration || 5000;

  useEffect(() => {
    // 入场动画
    const timer = setTimeout(() => setIsVisible(true), 50);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (isPaused || duration === 0) return;

    const interval = setInterval(() => {
      setProgress((prev) => {
        const newProgress = prev - (100 / (duration / 100));
        if (newProgress <= 0) {
          handleClose();
          return 0;
        }
        return newProgress;
      });
    }, 100);

    return () => clearInterval(interval);
  }, [isPaused, duration]);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => onClose(toast.id), 300);
  };

  const handleMouseEnter = () => {
    setIsPaused(true);
    onMouseEnter?.();
  };

  const handleMouseLeave = () => {
    setIsPaused(false);
    onMouseLeave?.();
  };

  return (
    <div
      className={`
        relative w-full max-w-sm mx-auto mb-4 p-4 rounded-xl border
        backdrop-blur-xl backdrop-saturate-150
        ${colors.bg} ${colors.border}
        shadow-lg shadow-black/5 dark:shadow-black/20
        transform transition-all duration-300 ease-out
        ${isVisible 
          ? 'translate-x-0 opacity-100 scale-100' 
          : 'translate-x-full opacity-0 scale-95'
        }
        hover:scale-[1.02] hover:shadow-xl
      `}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      role="alert"
      aria-live="polite"
    >
      {/* 毛玻璃边框增强效果 */}
      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-white/10 to-transparent pointer-events-none" />
      
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          <Icon className={`w-5 h-5 ${colors.icon}`} />
        </div>
        
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-1 drop-shadow-sm">
            {toast.title}
          </h4>
          {toast.message && (
            <p className="text-xs text-gray-700 dark:text-gray-200 leading-relaxed drop-shadow-sm">
              {toast.message}
            </p>
          )}
          
          {/* 操作按钮 */}
          {toast.actions && toast.actions.length > 0 && (
            <div className="flex space-x-2 mt-3">
              {toast.actions.map((action, index) => (
                <button
                  key={index}
                  onClick={action.onClick}
                  className={`
                    px-2 py-1 text-xs font-medium rounded-md
                    transition-colors duration-200
                    ${action.style === 'primary'
                      ? 'bg-primary text-primary-foreground hover:bg-primary/90'
                      : action.style === 'danger'
                      ? 'bg-red-500 text-white hover:bg-red-600'
                      : 'bg-muted text-muted-foreground hover:bg-muted/80'
                    }
                  `}
                >
                  {action.label}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* 关闭按钮 */}
        {toast.closable !== false && (
          <button
            onClick={handleClose}
            className="flex-shrink-0 p-1 rounded-md text-muted-foreground hover:text-foreground hover:bg-muted/50 transition-colors duration-200"
            aria-label="关闭通知"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>

      {/* 进度条 */}
      {duration > 0 && (
        <div className="absolute bottom-0 left-0 right-0 h-1 rounded-b-xl overflow-hidden">
          <div className="h-full bg-white/20" />
          <div
            className={`absolute top-0 left-0 h-full transition-all duration-100 ease-linear ${colors.progress}`}
            style={{ width: `${progress}%` }}
          />
        </div>
      )}
    </div>
  );
}