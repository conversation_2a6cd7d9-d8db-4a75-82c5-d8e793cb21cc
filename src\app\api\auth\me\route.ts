import { NextRequest, NextResponse } from 'next/server';
import { query, queryOne } from '@/lib/database';
import { getUserFromRequest, createApiResponse, createApiError } from '@/lib/utils';

export async function GET(request: NextRequest) {
  try {
    const user = await getUserFromRequest(request);
    
    if (!user) {
      return createApiError('UNAUTHORIZED', 'No valid session found', null, 401);
    }
    
    const userData = await queryOne(
      `SELECT 
        u.id,
        u.username,
        u.email,
        u.role_id,
        r.name as role,
        u.avatar_media_id,
        u.created_at,
        u.updated_at,
        (SELECT COUNT(*) FROM activity_logs WHERE user_id = u.id) as action_count
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.id
      WHERE u.id = ?`,
      [user.id]
    );
    
    if (!userData) {
      return createApiError('USER_NOT_FOUND', 'User not found', null, 404);
    }
    
    const permissions = await query(
      `SELECT p.name
       FROM permissions p
       JOIN role_permissions rp ON p.id = rp.permission_id
       WHERE rp.role_id = ?`,
      [userData.role_id]
    );
    
    const responseData = {
      user: {
        id: userData.id,
        username: userData.username,
        email: userData.email,
        role: userData.role,
        avatar: userData.avatar_media_id,
        actionCount: userData.action_count,
        createdAt: userData.created_at,
        updatedAt: userData.updated_at
      },
      permissions: permissions.map(p => p.name)
    };
    
    return createApiResponse(true, responseData);
    
  } catch (error) {
    console.error('Get user info error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'Failed to get user info', error, 500);
  }
}