import { query } from '@/lib/database';
import HomePage from '@/components/layout/HomePage';

async function getLinks() {
  try {
    const links = await query(`
      SELECT 
        l.id,
        l.title,
        l.url,
        l.description,
        l.category_id,
        c.name as category_name,
        l.is_private,
        l.icon_media_id,
        l.created_at,
        l.updated_at
      FROM links l
      LEFT JOIN categories c ON l.category_id = c.id
      WHERE l.is_private = false
      ORDER BY l.created_at DESC
    `);
    
    return links;
  } catch (error) {
    console.error('Error fetching links:', error);
    return [];
  }
}

async function getCategories() {
  try {
    const categories = await query(`
      SELECT 
        id,
        name,
        \`order\`,
        is_private,
        (SELECT COUNT(*) FROM links WHERE category_id = categories.id) as links_count
      FROM categories
      WHERE is_private = false
      ORDER BY \`order\` ASC, name ASC
    `);
    
    return categories;
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

export default async function Home() {
  try {
    const [links, categories] = await Promise.all([getLinks(), getCategories()]);
    console.log('Links loaded:', links.length, 'Categories loaded:', categories.length);
    return <HomePage initialLinks={links} initialCategories={categories} />;
  } catch (error) {
    console.error('Page error:', error);
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">页面加载错误</h1>
          <p className="text-gray-600">请检查数据库连接</p>
        </div>
      </div>
    );
  }
}