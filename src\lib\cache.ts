/**
 * 内存缓存工具 - 提升API响应性能
 * 解决页面加载慢的问题，特别是频繁访问的数据
 */

interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number; // 生存时间（毫秒）
}

class MemoryCache {
  private cache = new Map<string, CacheItem<any>>();
  private maxSize = 1000; // 最大缓存条目数
  
  /**
   * 设置缓存
   * @param key 缓存键
   * @param data 缓存数据
   * @param ttl 生存时间（秒），默认5分钟
   */
  set<T>(key: string, data: T, ttl: number = 300): void {
    const now = Date.now();
    
    // 如果缓存已满，清理过期条目
    if (this.cache.size >= this.maxSize) {
      this.cleanup();
      
      // 如果清理后仍然满了，删除最旧的条目
      if (this.cache.size >= this.maxSize) {
        const oldestKey = this.cache.keys().next().value;
        if (oldestKey) {
          this.cache.delete(oldestKey);
        }
      }
    }
    
    this.cache.set(key, {
      data,
      timestamp: now,
      ttl: ttl * 1000 // 转换为毫秒
    });
  }
  
  /**
   * 获取缓存
   * @param key 缓存键
   * @returns 缓存数据或undefined
   */
  get<T>(key: string): T | undefined {
    const item = this.cache.get(key);
    if (!item) return undefined;
    
    const now = Date.now();
    const isExpired = now - item.timestamp > item.ttl;
    
    if (isExpired) {
      this.cache.delete(key);
      return undefined;
    }
    
    return item.data as T;
  }
  
  /**
   * 删除缓存
   * @param key 缓存键
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }
  
  /**
   * 删除匹配模式的所有缓存
   * @param pattern 匹配模式
   */
  deletePattern(pattern: string): number {
    let count = 0;
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        this.cache.delete(key);
        count++;
      }
    }
    return count;
  }
  
  /**
   * 清理过期的缓存条目
   */
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      const isExpired = now - item.timestamp > item.ttl;
      if (isExpired) {
        this.cache.delete(key);
      }
    }
  }
  
  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear();
  }
  
  /**
   * 获取缓存统计信息
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize
    };
  }
}

// 创建全局缓存实例
const cache = new MemoryCache();

/**
 * 生成缓存键
 */
export const generateCacheKey = (prefix: string, params?: Record<string, any>): string => {
  if (!params || Object.keys(params).length === 0) {
    return prefix;
  }
  
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}:${params[key]}`)
    .join('|');
    
  return `${prefix}:${sortedParams}`;
};

/**
 * 缓存装饰器 - 用于API路由
 */
export const withCache = <T>(
  key: string,
  ttl: number = 300,
  fetcher: () => Promise<T>
) => {
  return async (): Promise<T> => {
    // 尝试从缓存获取
    const cached = cache.get<T>(key);
    if (cached !== undefined) {
      console.log(`Cache hit: ${key}`);
      return cached;
    }
    
    // 缓存未命中，执行fetcher
    console.log(`Cache miss: ${key}`);
    const data = await fetcher();
    
    // 缓存结果
    cache.set(key, data, ttl);
    
    return data;
  };
};

/**
 * 缓存失效策略
 */
export const invalidateCache = {
  // 网站相关缓存失效
  links: () => {
    cache.deletePattern('links:');
    cache.deletePattern('categories:'); // 分类中包含链接数量，也需要失效
    cache.deletePattern('statistics:');
    console.log('Invalidated links cache');
  },
  
  // 分类相关缓存失效
  categories: () => {
    cache.deletePattern('categories:');
    cache.deletePattern('links:'); // 链接查询依赖分类信息
    cache.deletePattern('statistics:');
    console.log('Invalidated categories cache');
  },
  
  // 统计数据缓存失效
  statistics: () => {
    cache.deletePattern('statistics:');
    console.log('Invalidated statistics cache');
  },
  
  // 用户相关缓存失效
  users: () => {
    cache.deletePattern('users:');
    cache.deletePattern('statistics:');
    console.log('Invalidated users cache');
  },
  
  // 图标相关缓存失效
  media: () => {
    cache.deletePattern('media:');
    cache.deletePattern('links:'); // 链接包含图标信息，也需要失效
    console.log('Invalidated media cache');
  },
  
  // 清空所有缓存
  all: () => {
    cache.clear();
    console.log('Cleared all cache');
  }
};

/**
 * 预热缓存 - 在应用启动时预加载热门数据
 */
export const warmupCache = {
  // 预热首页数据
  homepage: async () => {
    try {
      // 这里可以预加载首页常用的数据
      console.log('Cache warmup completed for homepage');
    } catch (error) {
      console.error('Cache warmup failed:', error);
    }
  }
};

/**
 * 缓存中间件 - 用于Next.js API路由
 */
export const cacheMiddleware = (
  keyGenerator: (url: string, user?: any) => string,
  ttl: number = 300
) => {
  return <T>(handler: (request: any) => Promise<T>) => {
    return async (request: any): Promise<T> => {
      const method = request.method || 'GET';
      
      // 只对GET请求进行缓存
      if (method !== 'GET') {
        return handler(request);
      }
      
      const cacheKey = keyGenerator(request.url, request.user);
      
      // 尝试从缓存获取
      const cached = cache.get<T>(cacheKey);
      if (cached !== undefined) {
        console.log(`API Cache hit: ${cacheKey}`);
        return cached;
      }
      
      // 缓存未命中，执行原始处理器
      console.log(`API Cache miss: ${cacheKey}`);
      const result = await handler(request);
      
      // 缓存结果（只缓存成功的响应）
      cache.set(cacheKey, result, ttl);
      
      return result;
    };
  };
};

/**
 * 定时清理过期缓存
 */
if (typeof window === 'undefined') { // 只在服务端运行
  setInterval(() => {
    cache.cleanup();
    console.log(`Cache cleanup completed. Current size: ${cache.getStats().size}`);
  }, 5 * 60 * 1000); // 每5分钟清理一次
}

export default cache;

/**
 * 缓存配置常量
 */
export const CACHE_TTL = {
  SHORT: 60,      // 1分钟 - 用于频繁变化的数据
  MEDIUM: 300,    // 5分钟 - 用于一般数据
  LONG: 3600,     // 1小时 - 用于相对稳定的数据
  VERY_LONG: 86400 // 24小时 - 用于很少变化的数据
} as const;