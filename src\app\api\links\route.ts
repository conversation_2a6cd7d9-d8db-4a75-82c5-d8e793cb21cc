import { NextRequest, NextResponse } from 'next/server';
import { query, queryOne } from '@/lib/database';
import { createApiResponse, createApiError, withAuth, withOptionalAuth } from '@/lib/utils';
import { generateCacheKey, withCache, invalidateCache, CACHE_TTL } from '@/lib/cache';

export const GET = withOptionalAuth(async (request: NextRequest & { user?: any }) => {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category');
    const isPrivate = searchParams.get('isPrivate');
    const sortBy = searchParams.get('sortBy') || 'created_at';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    
    // 调试信息
    console.log('=== LINKS API DEBUG ===');
    console.log('Request params:', { page, limit, search, category, isPrivate });
    
    // 获取用户信息
    const user = request.user;
    const isAuthenticated = !!user;
    const isSuperAdmin = user && user.role === 'super_admin';
    
    console.log('User info:', {
      isAuthenticated,
      isSuperAdmin,
      userId: user?.id,
      userRole: user?.role
    });
    
    // 生成缓存键
    const cacheKey = generateCacheKey('links', {
      page,
      limit,
      search,
      category,
      isPrivate,
      sortBy,
      sortOrder,
      userId: user?.id || 'anonymous',
      userRole: user?.role || 'anonymous'
    });
    
    console.log('Cache key:', cacheKey);
    
    // 使用缓存包装器
    const fetchLinksData = withCache(
      cacheKey,
      search ? CACHE_TTL.SHORT : CACHE_TTL.MEDIUM, // 搜索结果缓存时间短一些
      async () => {
        const offset = (page - 1) * limit;
        
        let whereClause = 'WHERE 1=1';
        const params: any[] = [];
        
        // 权限控制逻辑：
        console.log('Applying access control...');
        if (!isAuthenticated) {
          console.log('User not authenticated - showing only public links');
          whereClause += ' AND l.is_private = false';
        } else if (isSuperAdmin) {
          console.log('SuperAdmin user - showing all links');
          // 超级管理员可以看到所有链接
        } else {
          console.log('Authenticated regular/admin user - showing public + own private links');
          whereClause += ' AND (l.is_private = false OR (l.is_private = true AND l.user_id = ?))';
          params.push(user.id);
        }
        
        if (search) {
          whereClause += ' AND (l.title LIKE ? OR l.description LIKE ? OR l.url LIKE ?)';
          params.push(`%${search}%`, `%${search}%`, `%${search}%`);
        }
        
        if (category) {
          whereClause += ' AND l.category_id = ?';
          params.push(parseInt(category));
        }
        
        if (isAuthenticated && isPrivate !== null) {
          whereClause += ' AND l.is_private = ?';
          params.push(isPrivate === 'true');
        }
        
        const orderByClause = `ORDER BY l.${sortBy} ${sortOrder.toUpperCase()}`;
        
        const linksQuery = `
          SELECT 
            l.id,
            l.title,
            l.url,
            l.description,
            l.category_id,
            c.name as category_name,
            l.user_id,
            u.username as created_by,
            l.is_private,
            l.icon_media_id,
            m.file_path as icon_url,
            l.created_at,
            l.updated_at
          FROM links l
          LEFT JOIN categories c ON l.category_id = c.id
          LEFT JOIN users u ON l.user_id = u.id
          LEFT JOIN media m ON l.icon_media_id = m.id
          ${whereClause}
          ${orderByClause}
          LIMIT ? OFFSET ?
        `;
        
        const countQuery = `
          SELECT COUNT(*) as total
          FROM links l
          ${whereClause}
        `;
        
        console.log('Executing queries...');
        console.log('Links query:', linksQuery);
        console.log('Query params:', [...params, limit, offset]);
        
        const [links, countResult] = await Promise.all([
          query(linksQuery, [...params, limit, offset]),
          queryOne(countQuery, params)
        ]);
        
        console.log('Query results:');
        console.log('Links found:', links.length);
        console.log('Total count:', countResult.total);
        console.log('Sample links:', links.slice(0, 2));
        
        const total = countResult.total;
        const totalPages = Math.ceil(total / limit);
        
        const result = {
          links,
          total,
          page,
          totalPages
        };
        
        console.log('Final result:', { total, page, totalPages, linksCount: links.length });
        
        return result;
      }
    );
    
    const data = await fetchLinksData();
    console.log('=== LINKS API RESPONSE ===');
    console.log('Response data:', { total: data.total, page: data.page, linksCount: data.links.length });
    
    return createApiResponse(true, data);
    
  } catch (error) {
    console.error('Get links error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'Failed to fetch links', error, 500);
  }
});

export const POST = withAuth(async (request: NextRequest & { user: any }) => {
  try {
    const body = await request.json();
    const { title, url, description, categoryId, isPrivate, iconMediaId } = body;
    
    // 验证必填字段
    if (!title || !url) {
      return createApiError('VALIDATION_ERROR', 'Title and URL are required', null, 400);
    }

    // 放宽URL校验：仅校验非空，允许无 http/https 前缀的地址保存
    // 后续前端可提供“补全协议”按钮按需处理

    // 如果指定了分类，检查分类是否存在
    if (categoryId) {
      const categoryExists = await queryOne('SELECT id FROM categories WHERE id = ?', [categoryId]);
      if (!categoryExists) {
        return createApiError('CATEGORY_NOT_FOUND', 'Category not found', null, 400);
      }
    }
    
    // 如果指定了图标，检查图标是否存在
    if (iconMediaId) {
      const mediaExists = await queryOne('SELECT id FROM media WHERE id = ?', [iconMediaId]);
      if (!mediaExists) {
        return createApiError('MEDIA_NOT_FOUND', 'Media not found', null, 400);
      }
    }
    
    // 创建链接
    const result = await query(
      'INSERT INTO links (title, url, description, category_id, user_id, is_private, icon_media_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())',
      [title, url, description || '', categoryId || null, request.user.id, isPrivate ? 1 : 0, iconMediaId || null]
    );
    
    // 获取新创建的链接信息
    const newLink = await queryOne(
      `SELECT 
        l.id,
        l.title,
        l.url,
        l.description,
        l.category_id,
        c.name as category_name,
        l.user_id,
        u.username as created_by,
        l.is_private,
        l.icon_media_id,
        m.file_path as icon_url,
        l.created_at,
        l.updated_at
      FROM links l
      LEFT JOIN categories c ON l.category_id = c.id
      LEFT JOIN users u ON l.user_id = u.id
      LEFT JOIN media m ON l.icon_media_id = m.id
      WHERE l.id = ?`,
      [result.insertId]
    );
    
    // 记录操作日志
    await query(
      'INSERT INTO activity_logs (user_id, action, target_type, target_id, created_at) VALUES (?, ?, ?, ?, NOW())',
      [request.user.id, 'link_create', 'link', result.insertId]
    );
    
    // 缓存失效 - 清除相关缓存
    invalidateCache.links();
    
    return createApiResponse(true, newLink, 'Link created successfully', 201);
    
  } catch (error) {
    console.error('Create link error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'Failed to create link', error, 500);
  }
});