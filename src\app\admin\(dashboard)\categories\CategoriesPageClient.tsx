'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { Plus, Edit, Trash2, Search, RefreshCw, FolderOpen, Eye, EyeOff, ChevronUp, ChevronDown, GripVertical } from 'lucide-react';
import { CrudModal, ConfirmDeleteModal } from '@/components/admin/CrudModal';
import { Pagination } from '@/components/admin/Pagination';
import { formatDateSafe } from '@/lib/utils/dateUtils';
import { useToast } from '@/contexts/ToastContext';
import { useCategoriesWithPagination } from '@/hooks/useApiData';
import { AdminTableSkeleton } from '@/components/ui/SkeletonComponents';

interface Category {
  id: number;
  name: string;
  order: number;
  is_private: boolean;
  created_at: string;
  links_count: number;
}

interface CategoriesData {
  categories: Category[];
  total: number;
  page: number;
  totalPages: number;
}

const categoryFormFields = [
  {
    name: 'name',
    label: '分类名称',
    type: 'text' as const,
    placeholder: '请输入分类名称',
    required: true
  },
  {
    name: 'order',
    label: '排序',
    type: 'text' as const,
    placeholder: '数字越小排序越靠前',
    required: false
  },
  {
    name: 'is_private',
    label: '可见性',
    type: 'select' as const,
    required: true,
    options: [
      { label: '公开', value: '0' },
      { label: '私有', value: '1' }
    ]
  }
];

export default function CategoriesPageClient() {
  // 搜索和分页状态
  const [search, setSearch] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const { showSuccess, showError } = useToast();

  // 使用SWR进行数据获取
  const { categories: categoriesData, isLoading, mutate: mutateCategories } = useCategoriesWithPagination(
    currentPage,
    pageSize,
    search
  );

  // 视图层本地列表，避免revalidate造成闪屏
  const [categoriesView, setCategoriesView] = useState<Category[]>([]);
  useEffect(() => {
    // 当关键字段变化（而不仅仅是ID序列）时也要更新，避免“可见性仍显示公开”的问题
    const next = categoriesData.categories;
    const sameLength = categoriesView.length === next.length;
    const same = sameLength && categoriesView.every((c, i) => {
      const n = next[i];
      return (
        n &&
        c.id === n.id &&
        c.name === n.name &&
        c.order === n.order &&
        c.is_private === n.is_private &&
        c.links_count === n.links_count &&
        c.created_at === n.created_at
      );
    });
    if (!same) setCategoriesView(next);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [categoriesData.categories]);

  // 模态框状态
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [actionLoading, setActionLoading] = useState(false);


  // 搜索处理
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
  };

  // 分页处理
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  // 页大小变化处理
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
  };

  // 创建分类
  const handleCreateCategory = async (categoryData: Record<string, any>) => {
    setActionLoading(true);
    try {
      const response = await fetch('/api/categories', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: categoryData.name,
          order: categoryData.order ? parseInt(categoryData.order) : 0,
          isPrivate: categoryData.is_private === '1'
        })
      });
      
      if (response.ok) {
        showSuccess('创建成功', `分类"${categoryData.name}"创建成功`);
        setSearch('');
        setCurrentPage(1);
        mutateCategories();
        setIsCreateModalOpen(false);
      } else {
        const error = await response.json();
        showError('创建失败', error.error?.message || '创建分类时发生错误');
      }
    } catch (error) {
      console.error('Create category error:', error);
      showError('创建失败', '网络错误或服务器异常，请稍后重试');
    } finally {
      setActionLoading(false);
    }
  };

  // 更新分类
  const handleUpdateCategory = async (categoryData: Record<string, any>) => {
    if (!selectedCategory) return;
    
    setActionLoading(true);
    try {
      const response = await fetch(`/api/categories/${selectedCategory.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: categoryData.name,
          order: categoryData.order ? parseInt(categoryData.order) : 0,
          isPrivate: categoryData.is_private === '1'
        })
      });
      
      if (response.ok) {
        showSuccess('更新成功', `分类"${categoryData.name}"更新成功`);
        mutateCategories();
        setIsEditModalOpen(false);
        setSelectedCategory(null);
      } else {
        const error = await response.json();
        showError('更新失败', error.error?.message || '更新分类时发生错误');
      }
    } catch (error) {
      console.error('Update category error:', error);
      showError('更新失败', '网络错误或服务器异常，请稍后重试');
    } finally {
      setActionLoading(false);
    }
  };

  // 删除分类
  const handleDeleteCategory = async () => {
    if (!selectedCategory) return;
    
    setActionLoading(true);
    try {
      const response = await fetch(`/api/categories/${selectedCategory.id}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        showSuccess('删除成功', `分类"${selectedCategory.name}"已删除`);
        mutateCategories();
        setIsDeleteModalOpen(false);
        setSelectedCategory(null);
      } else {
        const error = await response.json();
        showError('删除失败', error.error?.message || '删除分类时发生错误');
      }
    } catch (error) {
      console.error('Delete category error:', error);
      showError('删除失败', '网络错误或服务器异常，请稍后重试');
    } finally {
      setActionLoading(false);
    }
  };

  // 编辑分类
  const handleEdit = (category: Category) => {
    setSelectedCategory(category);
    setIsEditModalOpen(true);
  };

  // 删除分类
  const handleDelete = (category: Category) => {
    setSelectedCategory(category);
    setIsDeleteModalOpen(true);
  };

  // 移动分类排序
  const handleMoveCategory = async (categoryId: number, direction: 'up' | 'down') => {
    setActionLoading(true);

    // 乐观更新：仅在当前页内有相邻目标时进行本地交换，跨页移动由服务端返回后刷新
    try {
      const list = categoriesView;
      const idx = list.findIndex((c: Category) => c.id === categoryId);
      const targetIdx = direction === 'up' ? idx - 1 : idx + 1;
      if (idx !== -1 && targetIdx >= 0 && targetIdx < list.length) {
        const newList = [...list];
        const [moved] = newList.splice(idx, 1);
        newList.splice(targetIdx, 0, moved);
        setCategoriesView(newList);
        await mutateCategories((prev: any) => {
          if (!prev) return prev;
          return { ...prev, data: { ...prev.data, categories: newList } };
        }, { revalidate: false });
      }

      const response = await fetch(`/api/categories/${categoryId}/move`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ direction })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error?.message || '移动分类失败');
      }

      showSuccess('排序更新', `分类排序已${direction === 'up' ? '上移' : '下移'}`);
    } catch (error) {
      console.error('Move category error:', error);
      showError('排序失败', error instanceof Error ? error.message : '移动分类时发生错误');
    } finally {
      // 无论成功失败，都强制刷新一次，防止乐观状态与服务端不一致（保留现有数据，后台校准）
      await mutateCategories((prev: any) => prev, { revalidate: true });
      setActionLoading(false);
    }
  };

  // 拖拽开始
  const handleDragStart = (e: React.DragEvent, categoryId: number) => {
    e.dataTransfer.setData('text/plain', categoryId.toString());
    e.dataTransfer.effectAllowed = 'move';
  };

  // 拖拽悬停
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  // 拖拽放下
  const handleDrop = async (e: React.DragEvent, targetCategoryId: number) => {
    e.preventDefault();
    const draggedStr = e.dataTransfer.getData('text/plain');
    if (!draggedStr) return;
    const draggedCategoryId = parseInt(draggedStr);
    if (!draggedCategoryId || draggedCategoryId === targetCategoryId) return;

    setActionLoading(true);

    // 乐观更新：仅当两个项都在当前页时执行本地移动
    try {
      const list = categoriesView;
      const from = list.findIndex((c: Category) => c.id === draggedCategoryId);
      const to = list.findIndex((c: Category) => c.id === targetCategoryId);
      if (from !== -1 && to !== -1) {
        const newList = [...list];
        const [moved] = newList.splice(from, 1);
        newList.splice(to, 0, moved);
        setCategoriesView(newList);
        await mutateCategories((prev: any) => {
          if (!prev) return prev;
          return { ...prev, data: { ...prev.data, categories: newList } };
        }, { revalidate: false });
      }

      const response = await fetch(`/api/categories/${draggedCategoryId}/reorder`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ targetId: targetCategoryId })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error?.message || '重新排序失败');
      }
    } catch (error) {
      console.error('Reorder category error:', error);
      showError('排序失败', error instanceof Error ? error.message : '重新排序时发生错误');
    } finally {
      // 强制刷新，确保与服务端顺序一致（保留现有数据，后台校准）
      await mutateCategories((prev: any) => prev, { revalidate: true });
      setActionLoading(false);
    }
  };


  return (
    <div>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 sm:mb-8 space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-foreground mb-1 sm:mb-2">
            分类管理
          </h1>
          <p className="text-sm sm:text-base text-muted-foreground">
            管理网站分类
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-0 sm:space-x-2">
          <Button variant="outline" onClick={() => mutateCategories()} className="w-full sm:w-auto">
            <RefreshCw className="w-4 h-4 mr-2" />
            刷新
          </Button>
          <Button onClick={() => setIsCreateModalOpen(true)} className="w-full sm:w-auto">
            <Plus className="w-4 h-4 mr-2" />
            新增分类
          </Button>
        </div>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg sm:text-xl">搜索分类</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-3 sm:gap-4">
            <div className="flex-1 relative">
              <Input
                type="text"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                placeholder="搜索分类名称..."
                className="pl-10"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            </div>
            <Button type="submit" size="default">
              <Search className="w-4 h-4 mr-2" />
              搜索
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>分类列表 ({categoriesData.total})</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <AdminTableSkeleton />
          ) : (
            <>
              {/* 桌面端表格 */}
              <div className="hidden lg:block overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-border">
                      <th className="text-left p-4 w-12">拖拽</th>
                      <th className="text-left p-4">分类</th>
                      <th className="text-left p-4">排序</th>
                      <th className="text-left p-4">可见性</th>
                      <th className="text-left p-4">网站数</th>
                      <th className="text-left p-4">创建时间</th>
                      <th className="text-left p-4">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {categoriesView.map((category: Category, index: number) => (
                      <tr
                        key={category.id} 
                        className="border-b border-border hover:bg-muted/50 cursor-move"
                        draggable
                        onDragStart={(e) => handleDragStart(e, category.id)}
                        onDragOver={handleDragOver}
                        onDrop={(e) => handleDrop(e, category.id)}
                      >
                        <td className="p-4">
                          <div className="flex items-center justify-center">
                            <GripVertical className="w-4 h-4 text-muted-foreground cursor-grab active:cursor-grabbing" />
                          </div>
                        </td>
                        <td className="p-4">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                              <FolderOpen className="w-4 h-4 text-primary" />
                            </div>
                            <div>
                              <p className="font-medium text-foreground">{category.name}</p>
                              <p className="text-sm text-muted-foreground">ID: {category.id}</p>
                            </div>
                          </div>
                        </td>
                        <td className="p-4">
                          <div className="flex items-center space-x-2">
                            <p className="text-sm text-foreground">{category.order}</p>
                            <div className="flex items-center space-x-1">
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-7 w-7 p-0 min-w-0 flex-shrink-0"
                                onClick={() => handleMoveCategory(category.id, 'up')}
                                disabled={actionLoading || (index === 0 && currentPage === 1)}
                              >
                                <ChevronUp className="w-3 h-3" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-7 w-7 p-0 min-w-0 flex-shrink-0"
                                onClick={() => handleMoveCategory(category.id, 'down')}
                                disabled={actionLoading || (index === categoriesData.categories.length - 1 && currentPage === categoriesData.totalPages)}
                              >
                                <ChevronDown className="w-3 h-3" />
                              </Button>
                            </div>
                          </div>
                        </td>
                        <td className="p-4">
                          <Badge variant={category.is_private ? 'secondary' : 'default'}>
                            <div className="flex items-center space-x-1">
                              {category.is_private ? (
                                <EyeOff className="w-3 h-3" />
                              ) : (
                                <Eye className="w-3 h-3" />
                              )}
                              <span>{category.is_private ? '私有' : '公开'}</span>
                            </div>
                          </Badge>
                        </td>
                        <td className="p-4">
                          <p className="text-sm text-muted-foreground">{category.links_count}</p>
                        </td>
                        <td className="p-4">
                          <p className="text-sm text-muted-foreground">
                            {formatDateSafe(category.created_at)}
                          </p>
                        </td>
                        <td className="p-4">
                          <div className="flex items-center space-x-2">
                            <Button variant="outline" size="sm" onClick={() => handleEdit(category)}>
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button variant="outline" size="sm" onClick={() => handleDelete(category)}>
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* 移动端卡片 */}
              <div className="lg:hidden space-y-4">
                {categoriesView.map((category: Category, index: number) => (
                  <Card key={category.id} className="p-4"
                    draggable
                    onDragStart={(e) => handleDragStart(e, category.id)}
                    onDragOver={handleDragOver}
                    onDrop={(e) => handleDrop(e, category.id)}
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                          <FolderOpen className="w-5 h-5 text-primary" />
                        </div>
                        <div>
                          <p className="font-medium text-foreground">{category.name}</p>
                          <p className="text-xs text-muted-foreground">ID: {category.id}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <GripVertical className="w-4 h-4 text-muted-foreground" />
                        <Badge variant={category.is_private ? 'secondary' : 'default'}>
                          <div className="flex items-center space-x-1">
                            {category.is_private ? (
                              <EyeOff className="w-3 h-3" />
                            ) : (
                              <Eye className="w-3 h-3" />
                            )}
                            <span>{category.is_private ? '私有' : '公开'}</span>
                          </div>
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-muted-foreground">排序</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-foreground">{category.order}</span>
                          <div className="flex space-x-0.5">
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-7 w-7 p-0 min-w-0 flex-shrink-0"
                              onClick={() => handleMoveCategory(category.id, 'up')}
                              disabled={actionLoading || (index === 0 && currentPage === 1)}
                            >
                              <ChevronUp className="w-3 h-3" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-7 w-7 p-0 min-w-0 flex-shrink-0"
                              onClick={() => handleMoveCategory(category.id, 'down')}
                              disabled={actionLoading || (index === categoriesData.categories.length - 1 && currentPage === categoriesData.totalPages)}
                            >
                              <ChevronDown className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-muted-foreground">网站数</span>
                        <span className="text-sm text-foreground">{category.links_count}</span>
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center mb-4">
                      <span className="text-xs text-muted-foreground">创建时间</span>
                      <span className="text-sm text-foreground">
                        {formatDateSafe(category.created_at)}
                      </span>
                    </div>
                    
                    <div className="flex space-x-2 pt-2 border-t border-border">
                      <Button variant="outline" size="sm" onClick={() => handleEdit(category)} className="flex-1">
                        <Edit className="w-4 h-4 mr-1" />
                        编辑
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => handleDelete(category)} className="flex-1">
                        <Trash2 className="w-4 h-4 mr-1" />
                        删除
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>

              {categoriesData.categories.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">暂无分类数据</p>
                </div>
              )}

              {categoriesData.totalPages > 1 && (
                <div className="mt-6">
                  <Pagination
                    currentPage={categoriesData.page}
                    totalPages={categoriesData.totalPages}
                    total={categoriesData.total}
                    pageSize={pageSize}
                    onPageChange={handlePageChange}
                    onPageSizeChange={handlePageSizeChange}
                    loading={isLoading}
                  />
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* 创建分类模态框 */}
      <CrudModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="新增分类"
        fields={categoryFormFields}
        onSubmit={handleCreateCategory}
        loading={actionLoading}
      />

      {/* 编辑分类模态框 */}
      <CrudModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedCategory(null);
        }}
        title="编辑分类"
        fields={categoryFormFields}
        initialData={selectedCategory ? {
          name: selectedCategory.name,
          order: selectedCategory.order.toString(),
          is_private: selectedCategory.is_private ? '1' : '0'
        } : {}}
        onSubmit={handleUpdateCategory}
        loading={actionLoading}
      />

      {/* 删除确认模态框 */}
      <ConfirmDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setSelectedCategory(null);
        }}
        title="删除分类"
        message={`确定要删除分类 "${selectedCategory?.name}" 吗？该分类下的 ${selectedCategory?.links_count || 0} 个网站需要先转移或删除，此操作不可恢复。`}
        onConfirm={handleDeleteCategory}
        loading={actionLoading}
      />
    </div>
  );
}