import { NextRequest } from 'next/server';
import { query, queryOne, transaction } from '@/lib/database';
import { createApiResponse, createApiError, withAuth } from '@/lib/utils';
import { invalidateCache } from '@/lib/cache';

// 移动分类排序（上移/下移）
export const POST = withAuth(async (request: NextRequest & { user: any }, { params }: { params: { id: string } }) => {
  try {
    const resolvedParams = await params;
    const categoryId = parseInt(resolvedParams.id);
    const body = await request.json();
    const { direction } = body;

    if (!['up', 'down'].includes(direction)) {
      return createApiError('VALIDATION_ERROR', '方向参数必须是 up 或 down', null, 400);
    }

    // 获取当前分类信息
    const currentCategory = await queryOne(
      'SELECT id, `order` FROM categories WHERE id = ?',
      [categoryId]
    );

    if (!currentCategory) {
      return createApiError('CATEGORY_NOT_FOUND', '分类不存在', null, 404);
    }

    // 稳健排序：基于全量顺序重新计算，避免重复order导致无法移动
    const allCategories: Array<{ id: number; order: number }> = await query(
      'SELECT id, `order` FROM categories ORDER BY `order` ASC, id ASC'
    );
    const idx = allCategories.findIndex(c => c.id === categoryId);
    const targetIdx = direction === 'up' ? idx - 1 : idx + 1;

    if (idx === -1 || targetIdx < 0 || targetIdx >= allCategories.length) {
      return createApiError('NO_MOVE_AVAILABLE', '该方向无法移动分类（已到边界）', null, 400);
    }

    const newOrderArr = [...allCategories];
    const [moved] = newOrderArr.splice(idx, 1);
    newOrderArr.splice(targetIdx, 0, moved);

    // 重新规范化所有 order 为 10 的倍数
    // 使用连接级事务保证移动与重排原子性
    await transaction(async (conn) => {
      for (let i = 0; i < newOrderArr.length; i++) {
        const expectedOrder = (i + 1) * 10;
        if (newOrderArr[i].order !== expectedOrder) {
          await conn.execute('UPDATE categories SET `order` = ? WHERE id = ?', [expectedOrder, newOrderArr[i].id]);
        }
      }
      await conn.execute(
        'INSERT INTO activity_logs (user_id, action, target_type, target_id, created_at) VALUES (?, ?, ?, ?, NOW())',
        [request.user.id, `category_move_${direction}`, 'category', categoryId]
      );
      return null;
    });

    // 移动完成后失效分类相关缓存，确保前端SWR刷新拿到最新顺序
    invalidateCache.categories();

    return createApiResponse(true, { id: categoryId, direction }, '分类移动成功');

  } catch (error) {
    console.error('Move category error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', '移动分类失败', error, 500);
  }
});