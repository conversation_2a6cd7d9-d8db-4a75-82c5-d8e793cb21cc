'use client';

import { useState, useEffect } from 'react';
import { 
  TrendingUp, TrendingDown, Users, Link, FolderOpen, Activity,
  BarChart3, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Gauge, Calendar
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import {
  UserGrowthChart,
  CategoryPieChart,
  VisitBarChart,
  ActivityGaugeChart,
  MultiLineChart
} from '@/components/charts/ChartComponents';

interface DashboardStats {
  metrics: {
    title: string;
    value: number;
    change: string;
    trend: 'up' | 'down';
    icon: string;
  }[];
  recent: {
    users: number;
    links: number;
  };
}

export default function AdminDashboardPage() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [userGrowthData, setUserGrowthData] = useState([]);
  const [categoryData, setCategoryData] = useState([]);
  const [visitData, setVisitData] = useState([]);
  const [activityData, setActivityData] = useState([]);
  const [trendData, setTrendData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState(30);

  const iconMap = {
    users: Users,
    links: Link,
    categories: FolderOpen,
    activity: Activity
  };

  useEffect(() => {
    fetchAllStats(timeRange);
  }, [timeRange]);

  const fetchAllStats = async (days: number) => {
    try {
      setLoading(true);
      
      const [
        overviewRes,
        userGrowthRes,
        categoryRes,
        visitRes,
        activityRes,
        trendRes
      ] = await Promise.all([
        fetch('/api/statistics?type=overview'),
        fetch(`/api/statistics?type=userGrowth&days=${days}`),
        fetch('/api/statistics?type=categoryStats'),
        fetch('/api/statistics?type=visitStats'),
        fetch('/api/statistics?type=activityStats'),
        fetch(`/api/statistics?type=trendStats&days=${days}`)
      ]);

      const [overview, userGrowth, category, visit, activity, trend] = 
        await Promise.all([
          overviewRes.json(),
          userGrowthRes.json(),
          categoryRes.json(),
          visitRes.json(),
          activityRes.json(),
          trendRes.json()
        ]);

      if (overview.success) setStats(overview.data);
      if (userGrowth.success) setUserGrowthData(userGrowth.data);
      if (category.success) setCategoryData(category.data);  
      if (visit.success) setVisitData(visit.data);
      if (activity.success) setActivityData(activity.data);
      if (trend.success) setTrendData(trend.data);
      
    } catch (error) {
      console.error('Failed to fetch dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-64 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded-lg"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-80 bg-muted rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-3 sm:p-4 lg:p-6 space-y-4 sm:space-y-6">
      {/* 页面头部 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl lg:text-4xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            <span className="hidden sm:inline">数据分析仪表盘</span>
            <span className="sm:hidden">数据仪表盘</span>
          </h1>
          <p className="text-muted-foreground mt-1 sm:mt-2 text-sm sm:text-base lg:text-lg">
            <span className="hidden sm:inline">实时监控系统数据，洞察业务趋势</span>
            <span className="sm:hidden">实时数据监控</span>
          </p>
        </div>
        
        <div className="flex items-center gap-1 sm:gap-2">
          <Button
            variant={timeRange === 7 ? "default" : "outline"}
            size="sm"
            onClick={() => setTimeRange(7)}
            className="text-xs sm:text-sm px-2 sm:px-3"
          >
            <span className="sm:hidden">7d</span>
            <span className="hidden sm:inline">7天</span>
          </Button>
          <Button
            variant={timeRange === 30 ? "default" : "outline"}
            size="sm"
            onClick={() => setTimeRange(30)}
            className="text-xs sm:text-sm px-2 sm:px-3"
          >
            <span className="sm:hidden">30d</span>
            <span className="hidden sm:inline">30天</span>
          </Button>
          <Button
            variant={timeRange === 90 ? "default" : "outline"}
            size="sm"
            onClick={() => setTimeRange(90)}
            className="text-xs sm:text-sm px-2 sm:px-3"
          >
            <span className="sm:hidden">90d</span>
            <span className="hidden sm:inline">90天</span>
          </Button>
        </div>
      </div>

      {/* 核心指标卡片 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
        {stats?.metrics.map((metric, index) => {
          const IconComponent = iconMap[metric.icon as keyof typeof iconMap] || Activity;
          const isPositive = metric.trend === 'up';
          
          return (
            <Card key={index} className="relative overflow-hidden transition-all duration-300 hover:shadow-lg">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-xs sm:text-sm font-medium text-muted-foreground truncate">
                  {metric.title}
                </CardTitle>
                <div className={`p-1 sm:p-2 rounded-lg ${isPositive ? 'bg-green-50 text-green-600' : 'bg-red-50 text-red-600'}`}>
                  <IconComponent className="h-3 w-3 sm:h-4 sm:w-4" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-lg sm:text-2xl lg:text-3xl font-bold text-foreground mb-1">
                  {metric.value.toLocaleString()}
                </div>
                <div className="flex items-center text-xs sm:text-sm">
                  {isPositive ? (
                    <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4 text-green-600 mr-1" />
                  ) : (
                    <TrendingDown className="h-3 w-3 sm:h-4 sm:w-4 text-red-600 mr-1" />
                  )}
                  <span className={isPositive ? 'text-green-600' : 'text-red-600'}>
                    {metric.change}
                  </span>
                  <span className="text-muted-foreground ml-1 hidden sm:inline">vs 上月</span>
                </div>
              </CardContent>
              {/* 装饰性渐变背景 */}
              <div className={`absolute inset-0 opacity-5 ${
                isPositive ? 'bg-gradient-to-br from-green-400 to-blue-500' : 'bg-gradient-to-br from-red-400 to-pink-500'
              }`} />
            </Card>
          );
        })}
      </div>

      {/* 图表区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4 lg:gap-6">
        {/* 用户增长趋势 */}
        <Card className="transition-all duration-300 hover:shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-sm sm:text-base">
              <LineChart className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
              <span className="hidden sm:inline">用户增长趋势</span>
              <span className="sm:hidden">用户增长</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <UserGrowthChart data={userGrowthData} height={250} className="sm:h-[300px]" />
          </CardContent>
        </Card>

        {/* 分类统计 */}
        <Card className="transition-all duration-300 hover:shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-sm sm:text-base">
              <PieChart className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
              <span className="hidden sm:inline">分类分布统计</span>
              <span className="sm:hidden">分类分布</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <CategoryPieChart data={categoryData} height={250} className="sm:h-[300px]" />
          </CardContent>
        </Card>

        {/* 访问统计 */}
        <Card className="transition-all duration-300 hover:shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-sm sm:text-base">
              <BarChart3 className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
              <span className="hidden sm:inline">访问量统计</span>
              <span className="sm:hidden">访问统计</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <VisitBarChart data={visitData} height={250} className="sm:h-[300px]" />
          </CardContent>
        </Card>

        {/* 实时活跃度 */}
        <Card className="transition-all duration-300 hover:shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-sm sm:text-base">
              <Gauge className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
              <span className="hidden sm:inline">实时活跃度</span>
              <span className="sm:hidden">活跃度</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="flex items-center justify-center">
            <ActivityGaugeChart data={activityData} height={200} className="sm:h-[250px]" />
          </CardContent>
        </Card>

        {/* 多维度趋势 */}
        <Card className="lg:col-span-2 transition-all duration-300 hover:shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-sm sm:text-base">
              <TrendingUp className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
              <span className="hidden sm:inline">多维度数据趋势</span>
              <span className="sm:hidden">数据趋势</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <MultiLineChart data={trendData} height={280} className="sm:h-[350px]" />
          </CardContent>
        </Card>

      </div>

      {/* 底部附加信息 */}
      <Card className="bg-gradient-to-r from-primary/5 to-purple-500/5 border-primary/20">
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 text-center">
            <div>
              <div className="text-xl sm:text-2xl font-bold text-primary">
                {stats?.recent.users || 0}
              </div>
              <div className="text-xs sm:text-sm text-muted-foreground">
                <span className="hidden sm:inline">本周新增用户</span>
                <span className="sm:hidden">新增用户</span>
              </div>
            </div>
            <div>
              <div className="text-xl sm:text-2xl font-bold text-primary">
                {stats?.recent.links || 0}
              </div>
              <div className="text-xs sm:text-sm text-muted-foreground">
                <span className="hidden sm:inline">本周新增网站</span>
                <span className="sm:hidden">新增网站</span>
              </div>
            </div>
            <div>
              <div className="text-xl sm:text-2xl font-bold text-primary">
                {((stats?.recent.users || 0) + (stats?.recent.links || 0))}
              </div>
              <div className="text-xs sm:text-sm text-muted-foreground">
                <span className="hidden sm:inline">总体活跃度</span>
                <span className="sm:hidden">活跃度</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}