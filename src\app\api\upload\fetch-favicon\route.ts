import { NextRequest, NextResponse } from 'next/server';
import { insert } from '@/lib/database';
import { createApiResponse, createApiError, getUserFromRequest } from '@/lib/utils';
import { invalidateCache } from '@/lib/cache';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';

interface FaviconFetchRequest {
  url: string;
  size?: number;
}

// 多个favicon获取策略，按优先级排序
const FAVICON_STRATEGIES = [
  // 1. 常用favicon服务
  { name: 'Google Favicons', url: (domain: string, size: number) => `https://www.google.com/s2/favicons?domain=${domain}&sz=${size}` },
  { name: 'DuckDuckGo Icons', url: (domain: string, size: number) => `https://icons.duckduckgo.com/ip3/${domain}.ico` },
  { name: 'Favicon.im', url: (domain: string, size: number) => `https://favicon.im/${domain}?larger=true` },
  
  // 2. 网站根目录favicon
  { name: 'Root favicon.ico', url: (domain: string) => `https://${domain}/favicon.ico` },
  { name: 'Root favicon.png', url: (domain: string) => `https://${domain}/favicon.png` },
  
  // 3. Apple Touch Icons
  { name: 'Apple Touch Icon', url: (domain: string) => `https://${domain}/apple-touch-icon.png` },
  { name: 'Apple Touch Icon 180x180', url: (domain: string) => `https://${domain}/apple-touch-icon-180x180.png` },
  { name: 'Apple Touch Icon 152x152', url: (domain: string) => `https://${domain}/apple-touch-icon-152x152.png` },
  
  // 4. 常见logo路径
  { name: 'Logo PNG', url: (domain: string) => `https://${domain}/logo.png` },
  { name: 'Logo SVG', url: (domain: string) => `https://${domain}/logo.svg` },
  { name: 'Assets Logo', url: (domain: string) => `https://${domain}/assets/logo.png` },
  { name: 'Images Logo', url: (domain: string) => `https://${domain}/images/logo.png` },
  
  // 5. 其他常见图标
  { name: 'Icon-192', url: (domain: string) => `https://${domain}/icon-192x192.png` },
  { name: 'Icon-512', url: (domain: string) => `https://${domain}/icon-512x512.png` },
  { name: 'Manifest Icon', url: (domain: string) => `https://${domain}/android-chrome-192x192.png` },
];

async function fetchFaviconFromUrl(url: string, size: number = 64, onProgress?: (progress: { current: number, total: number, strategy: string }) => void): Promise<{
  buffer: Buffer;
  contentType: string;
  source: string;
  strategy: string;
} | null> {
  try {
    const domain = new URL(url).hostname;
    
    // 尝试所有favicon获取策略
    for (let i = 0; i < FAVICON_STRATEGIES.length; i++) {
      const strategy = FAVICON_STRATEGIES[i];
      let faviconUrl: string;
      
      // 构建URL，某些策略支持size参数
      if (strategy.url.length === 2) {
        faviconUrl = strategy.url(domain, size);
      } else {
        faviconUrl = strategy.url(domain);
      }
      
      // 报告进度
      if (onProgress) {
        onProgress({
          current: i + 1,
          total: FAVICON_STRATEGIES.length,
          strategy: strategy.name
        });
      }
      
      try {
        console.log(`[${i + 1}/${FAVICON_STRATEGIES.length}] Trying ${strategy.name}: ${faviconUrl}`);
        
        const response = await fetch(faviconUrl, {
          method: 'GET',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cache-Control': 'no-cache',
          },
          // 增加超时时间到15秒，因为有更多策略要尝试
          signal: AbortSignal.timeout(15000),
        });

        if (response.ok && response.body) {
          const contentType = response.headers.get('content-type') || 'image/png';
          
          // 检查是否为图片类型
          if (contentType.startsWith('image/') || contentType.includes('octet-stream')) {
            const buffer = Buffer.from(await response.arrayBuffer());
            
            // 检查文件大小（最小100bytes，最大3MB）
            if (buffer.length < 100) {
              console.log(`${strategy.name}: File too small (${buffer.length} bytes)`);
              continue;
            }
            
            if (buffer.length > 3 * 1024 * 1024) {
              console.log(`${strategy.name}: File too large (${buffer.length} bytes)`);
              continue;
            }
            
            // 简单验证是否为有效图片（检查文件头）
            const isValidImage = isValidImageBuffer(buffer);
            if (!isValidImage) {
              console.log(`${strategy.name}: Invalid image format`);
              continue;
            }

            console.log(`✅ Success with ${strategy.name}: ${buffer.length} bytes, ${contentType}`);
            return {
              buffer,
              contentType,
              source: faviconUrl,
              strategy: strategy.name
            };
          } else {
            console.log(`${strategy.name}: Non-image response (${contentType})`);
          }
        } else {
          console.log(`${strategy.name}: HTTP ${response.status} ${response.statusText}`);
        }
      } catch (error: any) {
        console.log(`${strategy.name}: ${error.message}`);
        continue;
      }
    }
    
    console.log(`❌ All ${FAVICON_STRATEGIES.length} strategies failed for ${domain}`);
    return null;
  } catch (error) {
    console.error('Error in fetchFaviconFromUrl:', error);
    return null;
  }
}

// 验证图片buffer是否有效
function isValidImageBuffer(buffer: Buffer): boolean {
  if (buffer.length < 8) return false;
  
  // 检查常见图片格式的文件头
  const header = buffer.toString('hex', 0, 8).toLowerCase();
  
  return (
    header.startsWith('89504e47') || // PNG
    header.startsWith('ffd8ff') ||   // JPEG
    header.startsWith('47494638') || // GIF
    header.startsWith('52494646') || // WebP
    header.startsWith('3c3f786d') || // SVG (<?xml)
    header.startsWith('3c737667') || // SVG (<svg)
    header.startsWith('00000100') || // ICO
    header.startsWith('424d')        // BMP
  );
}

export async function POST(request: NextRequest) {
  try {
    const user = await getUserFromRequest(request);
    if (!user) {
      return createApiError('UNAUTHORIZED', 'Authentication required', null, 401);
    }

    const body = await request.json() as FaviconFetchRequest;
    const { url, size = 64 } = body;

    if (!url) {
      return createApiError('VALIDATION_ERROR', 'URL is required', null, 400);
    }

    // 验证URL格式
    try {
      new URL(url);
    } catch {
      return createApiError('VALIDATION_ERROR', 'Invalid URL format', null, 400);
    }

    // 获取favicon
    const faviconData = await fetchFaviconFromUrl(url, size);
    
    if (!faviconData) {
      return createApiError('FETCH_FAILED', 'Unable to fetch favicon from any source', null, 404);
    }

    // 正确映射 MIME 类型到文件扩展名
    const getFileExtension = (contentType: string): string => {
      const mimeToExt: Record<string, string> = {
        'image/png': 'png',
        'image/jpeg': 'jpg',
        'image/jpg': 'jpg',
        'image/gif': 'gif',
        'image/webp': 'webp',
        'image/svg+xml': 'svg',
        'image/x-icon': 'ico',
        'image/vnd.microsoft.icon': 'ico',
        'image/ico': 'ico',
        'image/icon': 'ico',
        'application/octet-stream': 'ico', // 某些服务器返回的ico格式
      };
      
      return mimeToExt[contentType] || 'png'; // 默认使用png扩展名
    };

    // 保存到文件系统
    const fileName = `favicon_${uuidv4()}.${getFileExtension(faviconData.contentType)}`;
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'icons');
    
    try {
      await mkdir(uploadDir, { recursive: true });
    } catch (error) {
      // Directory already exists
    }

    const filePath = join(uploadDir, fileName);
    await writeFile(filePath, faviconData.buffer);

    const fileUrl = `/uploads/icons/${fileName}`;

    // 保存到数据库
    const mediaId = await insert(
      'INSERT INTO media (file_path, file_type, file_size, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
      [fileUrl, faviconData.contentType, faviconData.buffer.length]
    );

    const responseData = {
      id: mediaId,
      fileName,
      fileSize: faviconData.buffer.length,
      fileType: faviconData.contentType,
      filePath: fileUrl,
      url: fileUrl,
      source: faviconData.source,
      strategy: faviconData.strategy,
      fetchedAt: new Date().toISOString()
    };

    // 缓存失效 - 清除相关缓存
    invalidateCache.media();

    return createApiResponse(true, responseData, 'Favicon fetched successfully', 201);

  } catch (error) {
    console.error('Favicon fetch error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'Failed to fetch favicon', error, 500);
  }
}