// 用户相关类型
export interface User {
  id: number;
  username: string;
  email: string;
  role_id: number;
  role: string;
  avatar_media_id?: string;
  created_at: string;
  updated_at: string;
  action_count?: number;
}

export interface UpdateProfileRequest {
  username: string;
  email: string;
  currentPassword?: string;
  newPassword?: string;
  avatar?: string;
}

// 链接相关类型
export interface Website {
  id: number;
  title: string;
  url: string;
  description?: string;
  category_id: number;
  category_name?: string;
  user_id?: number;
  created_by?: string;
  is_private: boolean;
  icon_media_id?: string;
  icon_url?: string;
  created_at: string;
  updated_at: string;
}

// 分类相关类型
export interface Category {
  id: number;
  name: string;
  order: number;
  icon_media_id?: string;
  user_id?: number;
  is_private: boolean;
  created_at: string;
  updated_at: string;
  links_count?: number;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}

// 统计数据类型
export interface DashboardMetric {
  title: string;
  value: number;
  change: string;
  trend: 'up' | 'down';
  icon: string;
}

export interface OverviewStats {
  metrics: DashboardMetric[];
  recent: {
    users: number;
    links: number;
  };
}

// Hook返回类型
export interface UseApiResult<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}
