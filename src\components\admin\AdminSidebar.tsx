'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  LayoutDashboard, 
  Users, 
  FolderOpen, 
  Link as LinkIcon, 
  Settings, 
  LogOut,
  Menu,
  X
} from 'lucide-react';

interface AdminSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const menuItems = [
  {
    title: '仪表盘',
    shortTitle: '仪表',
    href: '/admin/dashboard',
    icon: LayoutDashboard,
    permission: 'admin:dashboard:view'
  },
  {
    title: '用户管理',
    shortTitle: '用户',
    href: '/admin/users',
    icon: Users,
    permission: 'users:list'
  },
  {
    title: '分类管理',
    shortTitle: '分类',
    href: '/admin/categories',
    icon: FolderOpen,
    permission: 'categories:list'
  },
  {
    title: '网站管理',
    shortTitle: '网站',
    href: '/admin/links',
    icon: LinkIcon,
    permission: 'links:list'
  },
  {
    title: '权限设置',
    shortTitle: '权限',
    href: '/admin/permissions',
    icon: Settings,
    permission: 'roles:list'
  }
];

export default function AdminSidebar({ isOpen, onClose }: AdminSidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const pathname = usePathname();

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
      });
      window.location.href = '/admin/login';
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const handleLinkClick = () => {
    onClose(); // 移动端点击链接后关闭侧边栏
  };

  return (
    <>
      {/* 移动端侧边栏 */}
      <div className={`lg:hidden fixed inset-y-0 left-0 z-50 w-64 bg-background/95 backdrop-blur-md border-r border-border/50 transform transition-transform duration-300 ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="p-4 border-b border-border/50 pt-20">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-foreground">
              管理后台
            </h2>
            <button
              onClick={onClose}
              className="p-2 rounded-lg hover:bg-muted/80 transition-all duration-200"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>

        <nav className="p-4">
          <ul className="space-y-2">
            {menuItems.map((item) => {
              const isActive = pathname === item.href;

              return (
                <li key={item.href}>
                  <Link
                    href={item.href}
                    onClick={handleLinkClick}
                    className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 ${
                      isActive
                        ? 'bg-primary text-primary-foreground shadow-md'
                        : 'text-muted-foreground hover:bg-muted/80 hover:text-foreground'
                    }`}
                  >
                    <item.icon className="w-5 h-5" />
                    <span>{item.title}</span>
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>

        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-border/50 bg-background/50 backdrop-blur-sm">
          <button
            onClick={handleLogout}
            className="flex items-center space-x-3 w-full px-3 py-2 rounded-lg text-muted-foreground hover:bg-muted/80 hover:text-foreground transition-all duration-200"
          >
            <LogOut className="w-5 h-5" />
            <span>退出登录</span>
          </button>
        </div>
      </div>

      {/* 桌面端侧边栏 */}
      <div className={`hidden lg:block bg-background/80 backdrop-blur-md border-r border-border/50 transition-all duration-300 ${
        isCollapsed ? 'w-16' : 'w-64'
      }`}>
        <div className="p-4 border-b border-border/50">
          <div className="flex items-center justify-between">
            {!isCollapsed && (
              <h2 className="text-lg font-semibold text-foreground">
                管理后台
              </h2>
            )}
            <button
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="p-2 rounded-lg hover:bg-muted/80 transition-all duration-200"
            >
              {isCollapsed ? <Menu className="w-4 h-4" /> : <X className="w-4 h-4" />}
            </button>
          </div>
        </div>

        <nav className="p-4">
          <ul className="space-y-2">
            {menuItems.map((item) => {
              const isActive = pathname === item.href;

              return (
                <li key={item.href}>
                  <Link
                    href={item.href}
                    className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 ${
                      isActive
                        ? 'bg-primary text-primary-foreground shadow-md'
                        : 'text-muted-foreground hover:bg-muted/80 hover:text-foreground hover:translate-x-1'
                    }`}
                    title={isCollapsed ? item.title : undefined}
                  >
                    <item.icon className="w-5 h-5" />
                    {!isCollapsed && (
                      <span className="xl:hidden">{item.shortTitle}</span>
                    )}
                    {!isCollapsed && (
                      <span className="hidden xl:inline">{item.title}</span>
                    )}
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>

        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-border/50 bg-background/50 backdrop-blur-sm">
          <button
            onClick={handleLogout}
            className="flex items-center space-x-3 w-full px-3 py-2 rounded-lg text-muted-foreground hover:bg-muted/80 hover:text-foreground transition-all duration-200 hover:translate-x-1"
            title={isCollapsed ? '退出登录' : undefined}
          >
            <LogOut className="w-5 h-5" />
            {!isCollapsed && (
              <>
                <span className="xl:hidden">退出</span>
                <span className="hidden xl:inline">退出登录</span>
              </>
            )}
          </button>
        </div>
      </div>
    </>
  );
}