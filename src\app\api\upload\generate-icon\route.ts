import { NextRequest, NextResponse } from 'next/server';
import { queryOne, insert } from '@/lib/database';
import { createApiResponse, createApiError, getUserFromRequest } from '@/lib/utils';
import { invalidateCache } from '@/lib/cache';

interface IconGenerationRequest {
  title: string;
  url?: string;
  size?: number;
  backgroundColor?: string;
  textColor?: string;
}

function generateIconSvg(
  text: string,
  size: number = 64,
  backgroundColor?: string,
  textColor?: string
): string {
  const colors = [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
    '#06B6D4', '#F97316', '#84CC16', '#EC4899', '#6366F1'
  ];
  
  const bgColors = [
    '#DBEAFE', '#FEE2E2', '#D1FAE5', '#FEF3C7', '#EDE9FE',
    '#CFFAFE', '#FED7AA', '#D9F99D', '#FCE7F3', '#E0E7FF'
  ];
  
  const char = text.charAt(0).toUpperCase();
  
  const bgColor = backgroundColor || 
    bgColors[text.charCodeAt(0) % bgColors.length];
  const color = textColor || 
    colors[text.charCodeAt(0) % colors.length];
  
  const fontSize = Math.floor(size * 0.4);
  const centerY = size / 2;
  
  return `
    <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <style>
          .icon-text {
            font-family: "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", "Helvetica Neue", Arial, sans-serif;
            font-weight: bold;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
          }
        </style>
      </defs>
      <rect width="${size}" height="${size}" rx="${size * 0.2}" fill="${bgColor}" />
      <text x="${size / 2}" y="${centerY}" 
            text-anchor="middle" 
            dominant-baseline="central" 
            class="icon-text"
            font-size="${fontSize}" 
            fill="${color}">
        ${char}
      </text>
    </svg>
  `;
}

export async function POST(request: NextRequest) {
  try {
    const user = await getUserFromRequest(request);
    if (!user) {
      return createApiError('UNAUTHORIZED', 'Authentication required', null, 401);
    }

    const body = await request.json() as IconGenerationRequest;
    const { title, size = 64, backgroundColor, textColor } = body;

    if (!title || title.trim().length === 0) {
      return createApiError('VALIDATION_ERROR', 'Title is required', null, 400);
    }

    const svgContent = generateIconSvg(title, size, backgroundColor, textColor);
    // 确保中文字符正确编码 - 明确指定UTF-8编码
    const dataUrl = `data:image/svg+xml;base64,${Buffer.from(svgContent, 'utf8').toString('base64')}`;

    const mediaId = await insert(
      'INSERT INTO media (file_path, file_type, file_size, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
      [dataUrl, 'image/svg+xml', Buffer.byteLength(svgContent, 'utf8')]
    );

    const responseData = {
      id: mediaId,
      title,
      size,
      svgContent,
      dataUrl,
      createdAt: new Date().toISOString()
    };

    // 缓存失效 - 清除相关缓存
    invalidateCache.media();

    return createApiResponse(true, responseData, 'Icon generated successfully', 201);

  } catch (error) {
    console.error('Icon generation error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'Icon generation failed', error, 500);
  }
}