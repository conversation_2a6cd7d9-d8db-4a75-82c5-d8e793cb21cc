import { NextRequest, NextResponse } from 'next/server';
import { query, queryOne } from '@/lib/database';
import { createApiResponse, createApiError, withAuth } from '@/lib/utils';
import { generateCacheKey, withCache, invalidateCache, CACHE_TTL } from '@/lib/cache';

// 获取单个链接信息
export const GET = withAuth(async (request: NextRequest & { user: any }, { params }: { params: { id: string } }) => {
  try {
    const linkId = parseInt(params.id);
    
    // 生成缓存键
    const cacheKey = generateCacheKey('link', { id: linkId });
    
    // 使用缓存包装器
    const fetchLinkData = withCache(
      cacheKey,
      CACHE_TTL.MEDIUM,
      async () => {
        const link = await queryOne(`
          SELECT 
            l.id,
            l.title,
            l.url,
            l.description,
            l.category_id,
            c.name as category_name,
            l.is_private,
            l.icon_media_id,
            m.file_path as icon_url,
            l.created_at,
            l.updated_at
          FROM links l
          LEFT JOIN categories c ON l.category_id = c.id
          LEFT JOIN media m ON l.icon_media_id = m.id
          WHERE l.id = ?
        `, [linkId]);
        
        if (!link) {
          throw new Error('LINK_NOT_FOUND');
        }
        
        return link;
      }
    );
    
    try {
      const link = await fetchLinkData();
      return createApiResponse(true, link);
    } catch (error: any) {
      if (error.message === 'LINK_NOT_FOUND') {
        return createApiError('LINK_NOT_FOUND', 'Link not found', null, 404);
      }
      throw error;
    }
    
  } catch (error) {
    console.error('Get link error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'Failed to fetch link', error, 500);
  }
});

// 更新链接
export const PUT = withAuth(async (request: NextRequest & { user: any }, { params }: { params: { id: string } }) => {
  try {
    const linkId = parseInt(params.id);
    const body = await request.json();
    const { title, url, description, categoryId, isPrivate, iconMediaId } = body;

    // 验证必填字段
    if (!title || !url) {
      return createApiError('VALIDATION_ERROR', 'Title and URL are required', null, 400);
    }

    // 放宽URL校验：允许无 http/https 前缀
    // 原严格校验移除，仅保证非空

    // 检查链接是否存在
    const currentLink = await queryOne('SELECT id FROM links WHERE id = ?', [linkId]);
    if (!currentLink) {
      return createApiError('LINK_NOT_FOUND', 'Link not found', null, 404);
    }

    // 如果指定了分类，检查分类是否存在
    if (categoryId) {
      const category = await queryOne('SELECT id FROM categories WHERE id = ?', [categoryId]);
      if (!category) {
        return createApiError('CATEGORY_NOT_FOUND', 'Category not found', null, 400);
      }
    }

    // 如果指定了图标，检查图标是否存在
    if (iconMediaId) {
      const mediaExists = await queryOne('SELECT id FROM media WHERE id = ?', [iconMediaId]);
      if (!mediaExists) {
        return createApiError('MEDIA_NOT_FOUND', 'Media not found', null, 400);
      }
    }

    // 执行更新
    await query(`
      UPDATE links 
      SET title = ?, url = ?, description = ?, category_id = ?, is_private = ?, icon_media_id = ?, updated_at = NOW()
      WHERE id = ?
    `, [title, url, description || '', categoryId || null, isPrivate ? 1 : 0, iconMediaId || null, linkId]);

    // 获取更新后的链接信息
    const updatedLink = await queryOne(`
      SELECT 
        l.id,
        l.title,
        l.url,
        l.description,
        l.category_id,
        c.name as category_name,
        l.is_private,
        l.icon_media_id,
        m.file_path as icon_url,
        l.created_at,
        l.updated_at
      FROM links l
      LEFT JOIN categories c ON l.category_id = c.id
      LEFT JOIN media m ON l.icon_media_id = m.id
      WHERE l.id = ?
    `, [linkId]);

    // 记录操作日志
    await query(
      'INSERT INTO activity_logs (user_id, action, target_type, target_id, created_at) VALUES (?, ?, ?, ?, NOW())',
      [request.user.id, 'link_update', 'link', linkId]
    );
    
    // 缓存失效 - 清除相关缓存
    invalidateCache.links();

    return createApiResponse(true, updatedLink, 'Link updated successfully');
    
  } catch (error) {
    console.error('Update link error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'Failed to update link', error, 500);
  }
});

// 删除链接
export const DELETE = withAuth(async (request: NextRequest & { user: any }, { params }: { params: { id: string } }) => {
  try {
    const linkId = parseInt(params.id);
    
    // 检查链接是否存在
    const existingLink = await queryOne('SELECT id, title FROM links WHERE id = ?', [linkId]);
    if (!existingLink) {
      return createApiError('LINK_NOT_FOUND', 'Link not found', null, 404);
    }

    // 删除链接
    await query('DELETE FROM links WHERE id = ?', [linkId]);

    // 记录操作日志
    await query(
      'INSERT INTO activity_logs (user_id, action, target_type, target_id, created_at) VALUES (?, ?, ?, ?, NOW())',
      [request.user.id, 'link_delete', 'link', linkId]
    );
    
    // 缓存失效 - 清除相关缓存
    invalidateCache.links();

    return createApiResponse(true, { id: linkId }, 'Link deleted successfully');
    
  } catch (error) {
    console.error('Delete link error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'Failed to delete link', error, 500);
  }
});