import { NextRequest, NextResponse } from 'next/server';
import { getUserFromRequest, createApiResponse, createApiError } from '@/lib/utils';

// 处理登出逻辑的通用函数
async function handleLogout(request: NextRequest) {
  try {
    const user = await getUserFromRequest(request);
    
    if (!user) {
      return createApiError('UNAUTHORIZED', 'No valid session found', null, 401);
    }
    
    const response = createApiResponse(true, null, 'Logout successful');
    
    response.cookies.set('token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0,
      path: '/'
    });
    
    return response;
    
  } catch (error) {
    console.error('Logout error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'Logout failed', error, 500);
  }
}

export async function POST(request: NextRequest) {
  return handleLogout(request);
}

export async function GET(request: NextRequest) {
  const result = await handleLogout(request);
  
  // 对于GET请求，成功后重定向到首页
  if (result.status === 200) {
    return NextResponse.redirect(new URL('/', request.url));
  }
  
  return result;
}