'use client';

import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/Button';
import { X, Download, AlertCircle, CheckCircle } from 'lucide-react';

interface IconFetchProgressProps {
  isOpen: boolean;
  onClose: () => void;
  onCancel: () => void;
  website: {
    id: number;
    title: string;
    url: string;
  } | null;
  progress: {
    current: number;
    total: number;
    strategy: string;
    status: 'fetching' | 'success' | 'error' | 'cancelled';
    message?: string;
  };
}

export default function IconFetchProgress({
  isOpen,
  onClose,
  onCancel,
  website,
  progress
}: IconFetchProgressProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted || !isOpen || !website) {
    return null;
  }

  const progressPercentage = (progress.current / progress.total) * 100;
  const isCompleted = progress.status === 'success' || progress.status === 'error' || progress.status === 'cancelled';

  const getStatusIcon = () => {
    switch (progress.status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'cancelled':
        return <AlertCircle className="w-5 h-5 text-orange-500" />;
      default:
        return <Download className="w-5 h-5 text-blue-500 animate-pulse" />;
    }
  };

  const getStatusText = () => {
    switch (progress.status) {
      case 'success':
        return '获取成功';
      case 'error':
        return '获取失败';
      case 'cancelled':
        return '已取消';
      default:
        return '正在获取图标...';
    }
  };

  const getStatusColor = () => {
    switch (progress.status) {
      case 'success':
        return 'text-green-600 dark:text-green-400';
      case 'error':
        return 'text-red-600 dark:text-red-400';
      case 'cancelled':
        return 'text-orange-600 dark:text-orange-400';
      default:
        return 'text-blue-600 dark:text-blue-400';
    }
  };

  return (
    <>
      {/* 背景遮罩 */}
      <div className="fixed inset-0 bg-black/20 backdrop-blur-sm z-50" onClick={onClose} />
      
      {/* 进度弹窗 */}
      <div className="fixed left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 w-full max-w-md mx-4">
        <div className="bg-background/95 backdrop-blur-xl border border-border/50 rounded-lg shadow-2xl overflow-hidden"
             style={{
               background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
               backdropFilter: 'blur(20px)',
             }}>
          
          {/* 标题栏 */}
          <div className="flex items-center justify-between p-4 border-b border-border/30">
            <div className="flex items-center space-x-2">
              {getStatusIcon()}
              <h3 className="text-lg font-semibold text-foreground">
                {getStatusText()}
              </h3>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
          
          {/* 内容区域 */}
          <div className="p-6 space-y-4">
            {/* 网站信息 */}
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">正在为以下网站获取图标：</p>
              <div className="p-3 bg-muted/30 rounded-lg">
                <p className="font-medium text-foreground truncate" title={website.title}>
                  {website.title}
                </p>
                <p className="text-sm text-muted-foreground truncate" title={website.url}>
                  {website.url}
                </p>
              </div>
            </div>
            
            {/* 进度条 */}
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className={getStatusColor()}>
                  {progress.status === 'fetching' 
                    ? `尝试策略 ${progress.current}/${progress.total}`
                    : getStatusText()
                  }
                </span>
                <span className="text-muted-foreground">
                  {Math.round(progressPercentage)}%
                </span>
              </div>
              
              <div className="w-full bg-muted/30 rounded-full h-2 overflow-hidden">
                <div 
                  className={`h-full transition-all duration-300 ease-out ${
                    progress.status === 'success' 
                      ? 'bg-green-500' 
                      : progress.status === 'error' 
                        ? 'bg-red-500'
                        : progress.status === 'cancelled'
                          ? 'bg-orange-500'
                          : 'bg-blue-500'
                  }`}
                  style={{ width: `${progressPercentage}%` }}
                />
              </div>
            </div>
            
            {/* 当前策略 */}
            {progress.status === 'fetching' && (
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">当前尝试：</p>
                <p className="text-sm font-medium text-foreground bg-muted/20 rounded px-2 py-1">
                  {progress.strategy}
                </p>
              </div>
            )}
            
            {/* 结果消息 */}
            {progress.message && (
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">
                  {progress.status === 'success' ? '成功信息：' : '错误信息：'}
                </p>
                <p className={`text-sm ${getStatusColor()} bg-muted/20 rounded px-2 py-1`}>
                  {progress.message}
                </p>
              </div>
            )}
          </div>
          
          {/* 操作按钮 */}
          <div className="flex items-center justify-end space-x-2 p-4 border-t border-border/30">
            {!isCompleted ? (
              <>
                <Button variant="outline" onClick={onCancel}>
                  取消获取
                </Button>
              </>
            ) : (
              <Button onClick={onClose}>
                关闭
              </Button>
            )}
          </div>
        </div>
      </div>
    </>
  );
}