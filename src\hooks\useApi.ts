'use client';

import { useState, useEffect, useCallback } from 'react';

// 通用API响应类型
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp?: string;
}

// 数据获取状态
interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

// 通用API Hook
export function useApi<T = any>(
  url: string | null,
  options: RequestInit = {},
  dependencies: any[] = []
): UseApiState<T> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    if (!url) {
      setData(null);
      setLoading(false);
      setError(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      const result: ApiResponse<T> = await response.json();

      if (result.success && result.data !== undefined) {
        setData(result.data);
      } else {
        throw new Error(result.error?.message || '请求失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '网络错误');
      setData(null);
    } finally {
      setLoading(false);
    }
  }, [url, JSON.stringify(options)]);

  useEffect(() => {
    fetchData();
  }, [fetchData, ...dependencies]);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
  };
}

// 专用Hooks

// 获取统计数据
export function useStatistics(type: string, params: Record<string, any> = {}) {
  const queryString = new URLSearchParams(params).toString();
  const url = `/api/statistics?type=${type}${queryString ? '&' + queryString : ''}`;
  
  return useApi(url, {}, [type, JSON.stringify(params)]);
}

// 获取用户列表
export function useUsers(page = 1, limit = 10, search = '') {
  const url = `/api/users?page=${page}&limit=${limit}&search=${encodeURIComponent(search)}`;
  return useApi(url, {}, [page, limit, search]);
}

// 获取链接列表
export function useLinks(page = 1, limit = 10, categoryId?: number, search = '') {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    search: search,
  });
  
  if (categoryId) {
    params.append('categoryId', categoryId.toString());
  }
  
  const url = `/api/links?${params.toString()}`;
  return useApi(url, {}, [page, limit, categoryId, search]);
}

// 获取分类列表
export function useCategories() {
  return useApi('/api/categories');
}

// 获取当前用户信息
export function useCurrentUser() {
  return useApi('/api/auth/me');
}

// POST/PUT/DELETE操作Hook
interface UseMutationOptions<T, P> {
  onSuccess?: (data: T) => void;
  onError?: (error: string) => void;
}

export function useMutation<T = any, P = any>(
  url: string,
  method: 'POST' | 'PUT' | 'DELETE' = 'POST',
  options: UseMutationOptions<T, P> = {}
) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const mutate = useCallback(async (payload?: P) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: payload ? JSON.stringify(payload) : undefined,
      });

      const result: ApiResponse<T> = await response.json();

      if (result.success && result.data !== undefined) {
        options.onSuccess?.(result.data);
        return result.data;
      } else {
        throw new Error(result.error?.message || '操作失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '网络错误';
      setError(errorMessage);
      options.onError?.(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [url, method, options.onSuccess, options.onError]);

  return {
    mutate,
    loading,
    error,
  };
}

// 表单管理Hook
export function useForm<T extends Record<string, any>>(
  initialValues: T,
  onSubmit?: (values: T) => Promise<void> | void
) {
  const [values, setValues] = useState(initialValues);
  const [errors, setErrors] = useState<Partial<Record<keyof T, string>>>({});
  const [submitting, setSubmitting] = useState(false);

  const setValue = useCallback((name: keyof T, value: any) => {
    setValues(prev => ({ ...prev, [name]: value }));
    // 清除字段错误
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  }, [errors]);

  const setFieldError = useCallback((name: keyof T, error: string) => {
    setErrors(prev => ({ ...prev, [name]: error }));
  }, []);

  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  const reset = useCallback((newValues?: Partial<T>) => {
    setValues(prev => ({ ...prev, ...newValues }) || initialValues);
    setErrors({});
  }, [initialValues]);

  const handleSubmit = useCallback(async (e?: React.FormEvent) => {
    e?.preventDefault();
    
    if (!onSubmit) return;

    try {
      setSubmitting(true);
      await onSubmit(values);
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setSubmitting(false);
    }
  }, [values, onSubmit]);

  return {
    values,
    errors,
    submitting,
    setValue,
    setFieldError,
    clearErrors,
    reset,
    handleSubmit,
  };
}

// 本地存储Hook
export function useLocalStorage<T>(key: string, defaultValue: T) {
  const [value, setValue] = useState<T>(() => {
    if (typeof window === 'undefined') return defaultValue;
    
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return defaultValue;
    }
  });

  const setStoredValue = useCallback((newValue: T | ((prev: T) => T)) => {
    try {
      const valueToStore = newValue instanceof Function ? newValue(value) : newValue;
      setValue(valueToStore);
      
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  }, [key, value]);

  return [value, setStoredValue] as const;
}