import { query } from '@/lib/database';
import CategoriesPageClient from './CategoriesPageClient';

async function getCategories() {
  try {
    const categories = await query(`
      SELECT 
        id,
        name,
        \`order\`,
        is_private,
        (SELECT COUNT(*) FROM links WHERE category_id = categories.id) as links_count
      FROM categories
      WHERE is_private = false
      ORDER BY \`order\` ASC, name ASC
    `);
    
    return categories;
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

async function getAllLinks() {
  try {
    const links = await query(`
      SELECT 
        l.id,
        l.title,
        l.url,
        l.description,
        l.category_id,
        c.name as category_name,
        l.is_private,
        l.icon_media_id,
        l.created_at,
        l.updated_at
      FROM links l
      LEFT JOIN categories c ON l.category_id = c.id
      WHERE l.is_private = false
      ORDER BY l.created_at DESC
      LIMIT 100
    `);
    
    return links;
  } catch (error) {
    console.error('Error fetching all links:', error);
    return [];
  }
}

export default async function CategoriesPage() {
  const [categories, links] = await Promise.all([getCategories(), getAllLinks()]);

  return (
    <CategoriesPageClient
      initialCategories={categories}
      initialLinks={links}
    />
  );
}