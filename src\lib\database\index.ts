import mysql from 'mysql2/promise';

interface DatabaseConfig {
  host: string;
  port: number;
  user: string;
  password: string;
  database: string;
  waitForConnections: boolean;
  connectionLimit: number;
  queueLimit: number;
  ssl?: {
    rejectUnauthorized: boolean;
  };
}

const config: DatabaseConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root',
  database: process.env.DB_NAME || 'navigation_db',
  waitForConnections: true,
  connectionLimit: parseInt(process.env.DB_POOL_SIZE || '20'),
  queueLimit: 0,
  ssl: process.env.NODE_ENV === 'production' ? {
    rejectUnauthorized: true
  } : undefined
};

// 复用连接池避免开发环境热重载导致的连接泄漏
// eslint-disable-next-line no-var
declare global { var __MYSQL_POOL__: mysql.Pool | undefined; }
const pool = global.__MYSQL_POOL__ ?? mysql.createPool(config);
if (process.env.NODE_ENV !== 'production') { global.__MYSQL_POOL__ = pool; }

// 轻量重试器：对瞬时错误（连接数过多/连接断开等）进行指数退避重试
async function runWithRetry<T>(fn: () => Promise<T>, retries = 2): Promise<T> {
  try {
    return await fn();
  } catch (error: any) {
    const code = error?.code || '';
    const transient = code === 'ER_CON_COUNT_ERROR' || code === 'PROTOCOL_CONNECTION_LOST' || code === 'ECONNRESET' || code === 'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR';
    if (retries > 0 && transient) {
      const delay = (3 - retries) * 100; // 100ms, 200ms 回退
      await new Promise(r => setTimeout(r, delay));
      return runWithRetry(fn, retries - 1);
    }
    throw error;
  }
}

export async function query<T = any>(sql: string, params?: any[]): Promise<T[]> {
  try {
    const [rows] = await pool.execute(sql, params);
    return rows as T[];
  } catch (error: any) {
    const code = error?.code || '';
    // 连接过多/连接断开等瞬时错误，提示更友好
    if (code === 'ER_CON_COUNT_ERROR') {
      console.error('数据库连接过多，稍后重试');
    }
    console.error('Database query error:', error);
    throw new Error(`Database query failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function queryOne<T = any>(sql: string, params?: any[]): Promise<T | null> {
  try {
    const [rows] = await pool.execute(sql, params);
    return (rows as T[])[0] || null;
  } catch (error: any) {
    const code = error?.code || '';
    if (code === 'ER_CON_COUNT_ERROR') {
      console.error('数据库连接过多，稍后重试');
    }
    console.error('Database query error:', error);
    throw new Error(`Database query failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function insert(sql: string, params?: any[]): Promise<number> {
  try {
    const [result] = await pool.execute(sql, params);
    return (result as any).insertId;
  } catch (error: any) {
    const code = error?.code || '';
    if (code === 'ER_CON_COUNT_ERROR') {
      console.error('数据库连接过多，稍后重试');
    }
    console.error('Database insert error:', error);
    throw new Error(`Database insert failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function update(sql: string, params?: any[]): Promise<number> {
  try {
    const [result] = await pool.execute(sql, params);
    return (result as any).affectedRows;
  } catch (error: any) {
    const code = error?.code || '';
    if (code === 'ER_CON_COUNT_ERROR') {
      console.error('数据库连接过多，稍后重试');
    }
    console.error('Database update error:', error);
    throw new Error(`Database update failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function remove(sql: string, params?: any[]): Promise<number> {
  try {
    const [result] = await pool.execute(sql, params);
    return (result as any).affectedRows;
  } catch (error: any) {
    const code = error?.code || '';
    if (code === 'ER_CON_COUNT_ERROR') {
      console.error('数据库连接过多，稍后重试');
    }
    console.error('Database delete error:', error);
    throw new Error(`Database delete failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function transaction<T>(callback: (connection: mysql.Connection) => Promise<T>): Promise<T> {
  const connection = await pool.getConnection();
  try {
    await connection.beginTransaction();
    const result = await callback(connection);
    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    console.error('Database transaction error:', error);
    throw new Error(`Database transaction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    connection.release();
  }
}

export default pool;