'use client';

import { useState, useCallback, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { Plus, Edit, Trash2, Search, RefreshCw, Link as LinkIcon, ExternalLink, Eye, EyeOff, Download, Sparkles } from 'lucide-react';
import Image from 'next/image';
import { CrudModal, ConfirmDeleteModal } from '@/components/admin/CrudModal';
import IconSelector, { IconSelection } from '@/components/admin/IconSelector';
import { WebsiteModal } from '@/components/admin/WebsiteModal';
import { Pagination } from '@/components/admin/Pagination';
import IconFetchProgress from '@/components/admin/IconFetchProgress';
import { formatDateSafe } from '@/lib/utils/dateUtils';
import { useToast } from '@/contexts/ToastContext';
import { useWebsites, useCategories } from '@/hooks/useApiData';
import { AdminTableSkeleton } from '@/components/ui/SkeletonComponents';
import { useSmartPrefetch } from '@/lib/prefetch';

interface Website {
  id: number;
  title: string;
  url: string;
  description: string;
  category_id: number | null;
  category_name: string | null;
  is_private: boolean;
  icon_media_id: number | null;
  icon_url?: string | null;
  created_at: string;
  updated_at: string;
}

// 网站图标组件 - 与首页保持一致的显示优先级
function WebsiteIcon({ website, className }: { website: Website; className: string }) {
  const [imageError, setImageError] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const getFaviconUrl = (url: string) => {
    try {
      const domain = new URL(url).hostname;
      return `https://www.google.com/s2/favicons?domain=${domain}&sz=64`;
    } catch {
      return null;
    }
  };

  const getInitials = (title: string) => {
    return title.charAt(0).toUpperCase();
  };

  const getBackgroundColor = (title: string) => {
    const colors = [
      'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500',
      'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500',
      'bg-orange-500', 'bg-cyan-500', 'bg-lime-500', 'bg-rose-500'
    ];
    
    const index = title.charCodeAt(0) % colors.length;
    return colors[index];
  };

  if (!mounted) {
    return (
      <div className={`${className} rounded-lg bg-muted animate-pulse`} />
    );
  }

  // 优先级1：如果有后端返回的图标URL（可能是dataURL或静态路径），优先使用；否则回退到基于ID的静态路径
  const customIconUrl = website.icon_url || null;
  const faviconUrl = getFaviconUrl(website.url);

  if (customIconUrl && !imageError) {
    return (
      <img
        src={customIconUrl}
        alt={website.title}
        className={`${className} rounded-lg object-cover`}
        onError={() => setImageError(true)}
      />
    );
  }

  // 优先级2：如果自定义图标失败，尝试显示favicon
  if (faviconUrl && !imageError) {
    return (
      <img
        src={faviconUrl}
        alt={website.title}
        className={`${className} rounded-lg object-cover bg-white`}
        onError={() => setImageError(true)}
      />
    );
  }

  // 优先级3：Fallback到字母头像
  return (
    <div className={`${className} rounded-lg flex items-center justify-center text-white font-semibold text-sm sm:text-lg ${getBackgroundColor(website.title)}`}>
      {getInitials(website.title)}
    </div>
  );
}

interface Category {
  id: number;
  name: string;
}

export default function LinksPageClient() {
  // 搜索和筛选状态
  const [search, setSearch] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const { showSuccess, showError } = useToast();

  // 使用SWR进行数据获取
  const { websites: websitesData, isLoading: websitesLoading, mutate: mutateWebsites } = useWebsites(
    currentPage, 
    pageSize, 
    search, 
    selectedCategory
  );
  const { categories, isLoading: categoriesLoading } = useCategories();
  
  // 添加详细调试信息
  console.log('=== LinksPageClient 渲染调试 ===');
  console.log('websitesData:', websitesData);
  console.log('websitesLoading:', websitesLoading);
  console.log('categoriesLoading:', categoriesLoading);
  console.log('categories length:', categories.length);
  console.log('websites data structure:', {
    websitesExists: !!websitesData.websites,
    websitesArrayExists: !!websitesData.websites,
    websitesArrayLength: websitesData.websites?.length,
    isArray: Array.isArray(websitesData.websites)
  });
  
  // 检查加载状态
  const shouldShowLoading = websitesLoading || categoriesLoading;
  console.log('Should show loading:', shouldShowLoading, { websitesLoading, categoriesLoading });
  
  // 检查网站数据
  const websites = websitesData.websites || [];
  console.log('Final websites array:', websites);
  console.log('Sample website:', websites[0]);
  
  // 预取策略
  const { prefetchNextPage } = useSmartPrefetch();
  
  // 模态框状态
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedWebsite, setSelectedWebsite] = useState<Website | null>(null);
  const [actionLoading, setActionLoading] = useState(false);
  
  // 图标选择状态
  const [selectedIcon, setSelectedIcon] = useState<IconSelection>({ type: null, value: null, preview: '' });
  
  // 自动图标获取状态
  const [autoIconLoading, setAutoIconLoading] = useState<Set<number>>(new Set());
  
  // 进度弹窗状态
  const [progressModal, setProgressModal] = useState({
    isOpen: false,
    website: null as Website | null,
    progress: {
      current: 0,
      total: 15, // 总共15种获取策略
      strategy: '',
      status: 'fetching' as 'fetching' | 'success' | 'error' | 'cancelled',
      message: ''
    },
    abortController: null as AbortController | null
  });
  
  // 生成分类选项
  const categoryOptions = categories.length > 0 
    ? [{ label: '无分类', value: '' }, ...categories.map((cat: Category) => ({
        label: cat.name,
        value: cat.id.toString()
      }))]
    : [{ label: '无分类', value: '' }];

  // 动态生成表单字段
  const [websiteFormFields, setWebsiteFormFields] = useState([
    {
      name: 'title',
      label: '网站标题',
      type: 'text' as const,
      placeholder: '请输入网站标题',
      required: true
    },
    {
      name: 'url',
      label: '网站地址',
      type: 'url' as const,
      placeholder: '请输入完整的URL地址',
      required: true
    },
    {
      name: 'description',
      label: '描述',
      type: 'textarea' as const,
      placeholder: '请输入网站描述（可选）',
      required: false
    },
    {
      name: 'category_id',
      label: '分类',
      type: 'select' as const,
      required: false,
      options: []
    },
    {
      name: 'is_private',
      label: '可见性',
      type: 'select' as const,
      required: true,
      options: [
        { label: '公开', value: '0' },
        { label: '私有', value: '1' }
      ]
    },
    {
      name: 'icon',
      label: '网站图标',
      type: 'custom' as const,
      required: false,
      component: 'IconSelector'
    }
  ]);

  // 智能预取下一页数据
  useEffect(() => {
    if (websitesData.page && websitesData.totalPages) {
      prefetchNextPage(websitesData.page, websitesData.totalPages, {
        limit: pageSize,
        search,
        category: selectedCategory
      });
    }
  }, [websitesData.page, websitesData.totalPages, pageSize, search, selectedCategory, prefetchNextPage]);

  // 更新表单字段选项
  useEffect(() => {
    if (categories.length > 0) {
      const categoryOptions = categories.map((cat: Category) => ({
        label: cat.name,
        value: cat.id.toString()
      }));
      
      setWebsiteFormFields(prev => prev.map(field => 
        field.name === 'category_id' 
          ? { ...field, options: [{ label: '无分类', value: '' }, ...categoryOptions] }
          : field
      ));
    }
  }, [categories]);

  // 实时搜索效果
  useEffect(() => {
    const delayDebounce = setTimeout(() => {
      setCurrentPage(1);
    }, 500);

    return () => clearTimeout(delayDebounce);
  }, [search, selectedCategory]);

  // 统一错误中文提示
  const getFriendlyErrorMessage = (err: any, action: 'create' | 'update' = 'create') => {
    const e = err?.error || err || {};
    const code = e.code || '';
    const msg: string = e.message || '';

    // 常见业务错误映射
    if (code === 'VALIDATION_ERROR') return '请完整填写必填项';
    if (code === 'INVALID_URL') return 'URL 格式无效，请输入以 http(s):// 开头的地址，或点击“补全”按钮自动完善';
    if (code === 'CATEGORY_NOT_FOUND') return '所选分类不存在，请刷新后重新选择';
    if (code === 'MEDIA_NOT_FOUND') return '所选图标文件不存在，请重新选择或重新上传';

    // 数据库错误文案解析
    const text = `${msg}`;
    if (text.includes('cannot be null')) {
      if (/category_id/i.test(text)) return '分类不能为空，请选择一个分类';
      return '有必填项未填写，请检查表单';
    }
    if (/Duplicate entry/i.test(text)) return '该网址或名称已存在，请勿重复添加';
    if (/FOREIGN KEY constraint fails/i.test(text)) return '数据引用不合法，请检查所选分类或图标';

    if (code === 'INTERNAL_SERVER_ERROR' || code === 'DATABASE_ERROR') {
      return action === 'create' ? '服务器异常，创建失败，请稍后重试' : '服务器异常，更新失败，请稍后重试';
    }

    // 默认友好提示
    return action === 'create' ? '创建网站时发生错误' : '更新网站时发生错误';
  };

  // URL 标准化（无协议时默认 https）
  const normalizeUrl = (input: string): string => {
    const val = (input || '').trim();
    if (!val) return '';
    const hasScheme = /^[a-zA-Z][a-zA-Z0-9+.-]*:\/\//.test(val);
    return hasScheme ? val : `https://${val}`;
  };

  // 搜索处理
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
  };

  // 分类筛选处理
  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategory(categoryId);
    setCurrentPage(1);
  };

  // 分页处理
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  // 页大小变化处理
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
  };

  // 创建网站
  const handleCreateWebsite = async (websiteData: Record<string, any>) => {
    setActionLoading(true);
    try {
      const response = await fetch('/api/links', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: websiteData.title,
          url: normalizeUrl(websiteData.url),
          description: websiteData.description || '',
          categoryId: websiteData.category_id ? parseInt(websiteData.category_id) : null,
          isPrivate: websiteData.is_private === '1',
          iconMediaId: selectedIcon.mediaId || null
        })
      });
      
      if (response.ok) {
        showSuccess('创建成功', `网站"${websiteData.title}"创建成功`);
        // 清空搜索和分类筛选，回到第一页显示新创建的网站
        setSearch('');
        setSelectedCategory('');
        setCurrentPage(1);
        mutateWebsites();
        setIsCreateModalOpen(false);
        setSelectedIcon({ type: null, value: null, preview: '' }); // 重置图标选择
      } else {
        const error = await response.json();
        showError('创建失败', getFriendlyErrorMessage(error, 'create'));
      }
    } catch (error) {
      console.error('Create link error:', error);
      showError('创建失败', '网络错误或服务器异常，请稍后重试');
    } finally {
      setActionLoading(false);
    }
  };

  // 更新网站
  const handleUpdateWebsite = async (websiteData: Record<string, any>) => {
    if (!selectedWebsite) return;
    
    setActionLoading(true);
    try {
      const response = await fetch(`/api/links/${selectedWebsite.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: websiteData.title,
          url: normalizeUrl(websiteData.url),
          description: websiteData.description || '',
          categoryId: websiteData.category_id ? parseInt(websiteData.category_id) : null,
          isPrivate: websiteData.is_private === '1',
          iconMediaId: selectedIcon.mediaId || null
        })
      });
      
      if (response.ok) {
        showSuccess('更新成功', `网站"${websiteData.title}"更新成功`);
        mutateWebsites();
        setIsEditModalOpen(false);
        setSelectedWebsite(null);
        setSelectedIcon({ type: null, value: null, preview: '' }); // 重置图标选择
      } else {
        const error = await response.json();
        showError('更新失败', getFriendlyErrorMessage(error, 'update'));
      }
    } catch (error) {
      console.error('Update link error:', error);
      showError('更新失败', '网络错误或服务器异常，请稍后重试');
    } finally {
      setActionLoading(false);
    }
  };

  // 删除网站
  const handleDeleteWebsite = async () => {
    if (!selectedWebsite) return;
    
    setActionLoading(true);
    try {
      const response = await fetch(`/api/links/${selectedWebsite.id}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        showSuccess('删除成功', `网站"${selectedWebsite.title}"已删除`);
        mutateWebsites();
        setIsDeleteModalOpen(false);
        setSelectedWebsite(null);
      } else {
        const error = await response.json();
        showError('删除失败', error.error?.message || '删除网站时发生错误');
      }
    } catch (error) {
      console.error('Delete website error:', error);
      showError('删除失败', '网络错误或服务器异常，请稍后重试');
    } finally {
      setActionLoading(false);
    }
  };

  // 图标选择处理
  const handleIconSelect = (icon: IconSelection) => {
    setSelectedIcon(icon);
  };
  
  // 清理图标选择
  const clearIconSelection = () => {
    setSelectedIcon({ type: null, value: null, preview: '' });
  };
  
  // 自动获取网站图标
  const handleAutoFetchIcon = async (website: Website) => {
    setAutoIconLoading(prev => new Set([...prev, website.id]));
    
    // 创建AbortController用于取消请求
    const abortController = new AbortController();
    
    // 打开进度弹窗
    setProgressModal({
      isOpen: true,
      website,
      progress: {
        current: 0,
        total: 15,
        strategy: '准备中...',
        status: 'fetching',
        message: ''
      },
      abortController
    });
    
    try {
      // 模拟进度更新（因为当前API不支持实时进度回调）
      let currentStep = 0;
      const totalSteps = 15;
      
      const updateProgress = (strategy: string, step: number) => {
        setProgressModal(prev => ({
          ...prev,
          progress: {
            ...prev.progress,
            current: step,
            strategy
          }
        }));
      };
      
      // 开始获取favicon
      updateProgress('正在尝试多种策略获取图标...', 1);
      
      const faviconResponse = await fetch('/api/upload/fetch-favicon', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url: website.url, size: 64 }),
        signal: abortController.signal
      });
      
      let iconMediaId = null;
      
      if (faviconResponse.ok) {
        const faviconResult = await faviconResponse.json();
        if (faviconResult.success) {
          iconMediaId = faviconResult.data.id;
          updateProgress(`成功获取 - ${faviconResult.data.strategy}`, totalSteps);
          
          // 设置成功状态
          setProgressModal(prev => ({
            ...prev,
            progress: {
              ...prev.progress,
              current: totalSteps,
              status: 'success',
              message: `已从 ${faviconResult.data.strategy} 获取到图标`
            }
          }));
          
          showSuccess('图标获取成功', `已从 ${faviconResult.data.strategy} 获取到图标`);
        }
      }
      
      // 如果 favicon 获取失败，则生成字母图标
      if (!iconMediaId && !abortController.signal.aborted) {
        updateProgress('正在生成字母图标...', totalSteps - 2);
        
        const generateResponse = await fetch('/api/upload/generate-icon', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            title: website.title,
            size: 64
          }),
          signal: abortController.signal
        });
        
        if (generateResponse.ok) {
          const generateResult = await generateResponse.json();
          if (generateResult.success) {
            iconMediaId = generateResult.data.id;
            
            setProgressModal(prev => ({
              ...prev,
              progress: {
                ...prev.progress,
                current: totalSteps,
                status: 'success',
                message: `已为"${website.title}"生成字母图标`
              }
            }));
            
            showSuccess('字母图标生成成功', `已为"${website.title}"生成字母图标`);
          }
        }
      }
      
      // 如果成功获取到图标，则更新网站
      if (iconMediaId && !abortController.signal.aborted) {
        updateProgress('正在保存图标...', totalSteps - 1);
        
        const updateResponse = await fetch(`/api/links/${website.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            title: website.title,
            url: website.url,
            description: website.description || '',
            categoryId: website.category_id,
            isPrivate: website.is_private,
            iconMediaId
          }),
          signal: abortController.signal
        });
        
        if (updateResponse.ok) {
          mutateWebsites(); // 刷新网站列表
          updateProgress('完成！', totalSteps);
        } else {
          setProgressModal(prev => ({
            ...prev,
            progress: {
              ...prev.progress,
              status: 'error',
              message: '获取图标成功，但更新网站失败'
            }
          }));
          showError('更新失败', '获取图标成功，但更新网站失败');
        }
      } else if (!iconMediaId && !abortController.signal.aborted) {
        setProgressModal(prev => ({
          ...prev,
          progress: {
            ...prev.progress,
            status: 'error',
            message: '无法从URL获取图标，也无法生成字母图标'
          }
        }));
        showError('获取图标失败', '无法从URL获取图标，也无法生成字母图标');
      }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        setProgressModal(prev => ({
          ...prev,
          progress: {
            ...prev.progress,
            status: 'cancelled',
            message: '用户取消操作'
          }
        }));
        showError('操作取消', '图标获取已取消');
      } else {
        console.error('Auto fetch icon error:', error);
        setProgressModal(prev => ({
          ...prev,
          progress: {
            ...prev.progress,
            status: 'error',
            message: '网络错误或服务器异常'
          }
        }));
        showError('获取图标失败', '网络错误或服务器异常，请稍后重试');
      }
    } finally {
      setAutoIconLoading(prev => {
        const newSet = new Set(prev);
        newSet.delete(website.id);
        return newSet;
      });
    }
  };

  // 编辑网站
  const handleEdit = (website: Website) => {
    setSelectedWebsite(website);

    // 计算与列表一致的favicon回退
    const computeFavicon = (url: string) => {
      try {
        const domain = new URL(url).hostname;
        return `https://www.google.com/s2/favicons?domain=${domain}&sz=64`;
      } catch {
        return '';
      }
    };

    // 初始化图标预览：优先 icon_url，其次 favicon；同时保留 mediaId
    const preview = website.icon_url || computeFavicon(website.url) || '';
    const initialIcon: IconSelection = website.icon_media_id
      ? { type: null, value: null, preview, mediaId: Number(website.icon_media_id) }
      : { type: null, value: null, preview };

    setSelectedIcon(initialIcon);
    setIsEditModalOpen(true);
  };

  // 删除网站
  const handleDelete = (website: Website) => {
    setSelectedWebsite(website);
    setIsDeleteModalOpen(true);
  };
  
  // 取消图标获取
  const handleCancelIconFetch = () => {
    if (progressModal.abortController) {
      progressModal.abortController.abort();
    }
  };
  
  // 关闭进度弹窗
  const handleCloseProgressModal = () => {
    setProgressModal(prev => ({
      ...prev,
      isOpen: false,
      website: null,
      abortController: null
    }));
  };

  return (
    <div>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 sm:mb-8 space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-foreground mb-1 sm:mb-2">
            网站管理
          </h1>
          <p className="text-sm sm:text-base text-muted-foreground">
            管理网站和资源
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-0 sm:space-x-2">
          <Button variant="outline" onClick={() => mutateWebsites()} className="w-full sm:w-auto">
            <RefreshCw className="w-4 h-4 mr-2" />
            刷新
          </Button>
          <Button onClick={() => setIsCreateModalOpen(true)} className="w-full sm:w-auto">
            <Plus className="w-4 h-4 mr-2" />
            新增网站
          </Button>
        </div>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg sm:text-xl">搜索和筛选</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-3 sm:gap-4">
            <div className="flex-1 relative">
              <Input
                type="text"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                placeholder="搜索网站标题、URL或描述..."
                className="pl-10"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            </div>
            
            <select 
              value={selectedCategory}
              onChange={(e) => handleCategoryChange(e.target.value)}
              className="px-3 py-2 border border-border rounded-lg bg-background text-foreground text-sm"
            >
              <option value="">所有分类</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id.toString()}>
                  {category.name}
                </option>
              ))}
            </select>
            
            <Button type="submit" size="default">
              <Search className="w-4 h-4 mr-2" />
              搜索
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>网站列表 ({websitesData.total || 0})</CardTitle>
        </CardHeader>
        <CardContent>
          {(() => {
            console.log('=== 渲染条件检查 ===');
            console.log('websitesLoading:', websitesLoading);
            console.log('categoriesLoading:', categoriesLoading);
            console.log('shouldShowLoading:', websitesLoading || categoriesLoading);
            
            if (websitesLoading || categoriesLoading) {
              console.log('显示骨架屏 - Loading状态');
              return <AdminTableSkeleton />;
            }
            
            console.log('准备渲染数据');
            console.log('websites array:', websitesData.websites);
            console.log('websites length:', websitesData.websites?.length);
            
            return (
              <>
                <div className="space-y-4">
                  {(() => {
                    console.log('=== 开始渲染网站列表 ===');
                    const websitesList = websitesData.websites;
                    console.log('即将map的数组:', websitesList);
                    
                    if (!websitesList) {
                      console.log('websitesList 为空');
                      return <div>websitesList 为空</div>;
                    }
                    
                    if (!Array.isArray(websitesList)) {
                      console.log('websitesList 不是数组:', typeof websitesList);
                      return <div>websitesList 不是数组</div>;
                    }
                    
                    if (websitesList.length === 0) {
                      console.log('websitesList 数组为空');
                      return <div>websitesList 数组为空</div>;
                    }
                    
                    console.log('开始映射', websitesList.length, '个网站');
                    return websitesList.map((website, index) => {
                      console.log(`渲染网站 ${index}:`, website);
                      return (
                        <div
                          key={website.id}
                          className="flex flex-col lg:flex-row lg:items-start lg:justify-between p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors space-y-3 lg:space-y-0"
                        >
                          <div className="flex items-start space-x-3 sm:space-x-4 flex-1 min-w-0">
                            <WebsiteIcon 
                              website={website} 
                              className="w-10 h-10 sm:w-12 sm:h-12"
                            />
                            
                            <div className="flex-1 min-w-0">
                              <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 mb-2">
                                <h3 className="font-semibold text-foreground text-sm sm:text-base truncate">
                                  {website.title}
                                </h3>
                                <div className="flex flex-wrap gap-2">
                                  <Badge variant={website.is_private ? "secondary" : "default"} className="text-xs">
                                    <div className="flex items-center space-x-1">
                                      {website.is_private ? (
                                        <EyeOff className="w-3 h-3" />
                                      ) : (
                                        <Eye className="w-3 h-3" />
                                      )}
                                      <span>{website.is_private ? '私有' : '公开'}</span>
                                    </div>
                                  </Badge>
                                  {website.category_name && (
                                    <Badge variant="outline" className="text-xs">
                                      {website.category_name}
                                    </Badge>
                                  )}
                                </div>
                              </div>
                              
                              {website.description && (
                                <p className="text-xs sm:text-sm text-muted-foreground mb-2 line-clamp-2">
                                  {website.description}
                                </p>
                              )}
                              
                              <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4 text-xs sm:text-sm text-muted-foreground">
                                <span className="hidden sm:inline flex-shrink-0">ID: {website.id}</span>
                                <a
                                  href={(function(u){ try { return new URL(u).toString(); } catch { return u ? `https://${u}` : '#'; } })(website.url)}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="flex items-center space-x-1 text-primary hover:underline min-w-0"
                                >
                                  <ExternalLink className="w-3 h-3 flex-shrink-0" />
                                  <span className="truncate max-w-[150px] sm:max-w-[200px]">{website.url}</span>
                                </a>
                                <span className="flex-shrink-0 text-xs">{formatDateSafe(website.created_at)}</span>
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex items-center space-x-2 lg:ml-4 justify-end lg:justify-start">
                            {!website.icon_media_id && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleAutoFetchIcon(website)}
                                disabled={autoIconLoading.has(website.id)}
                                className="flex-1 lg:flex-none"
                                title="自动获取网站图标"
                              >
                                {autoIconLoading.has(website.id) ? '获取中…' : '获取图标'}
                              </Button>
                            )}
                            <Button variant="outline" size="sm" onClick={() => handleEdit(website)} className="flex-1 lg:flex-none">
                              <Edit className="w-4 h-4 mr-1 lg:mr-0" />
                              <span className="lg:hidden">编辑</span>
                            </Button>
                            <Button variant="outline" size="sm" onClick={() => handleDelete(website)} className="flex-1 lg:flex-none">
                              <Trash2 className="w-4 h-4 mr-1 lg:mr-0" />
                              <span className="lg:hidden">删除</span>
                            </Button>
                          </div>
                        </div>
                      );
                    });
                  })()}
                </div>

                {(websitesData.websites?.length || 0) === 0 && (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">暂无网站数据</p>
                  </div>
                )}

                {(websitesData.totalPages || 0) > 1 && (
                  <div className="mt-6">
                    <Pagination
                      currentPage={websitesData.page || 1}
                      totalPages={websitesData.totalPages || 0}
                      total={websitesData.total || 0}
                      pageSize={pageSize}
                      onPageChange={handlePageChange}
                      onPageSizeChange={handlePageSizeChange}
                      loading={websitesLoading}
                    />
                  </div>
                )}
              </>
            );
          })()}
        </CardContent>
      </Card>

      {/* 创建网站模态框 */}
      <WebsiteModal
        isOpen={isCreateModalOpen}
        onClose={() => {
          setIsCreateModalOpen(false);
          setSelectedIcon({ type: null, value: null, preview: '' });
        }}
        title="新增网站"
        categoryOptions={categoryOptions}
        onSubmit={handleCreateWebsite}
        onIconSelect={handleIconSelect}
        selectedIcon={selectedIcon}
        loading={actionLoading}
        requireCategory
      />

      {/* 编辑网站模态框 */}
      <WebsiteModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedWebsite(null);
          setSelectedIcon({ type: null, value: null, preview: '' });
        }}
        title="编辑网站"
        categoryOptions={categoryOptions}
        initialData={selectedWebsite ? {
          title: selectedWebsite.title,
          url: selectedWebsite.url,
          description: selectedWebsite.description,
          category_id: selectedWebsite.category_id?.toString() || '',
          is_private: selectedWebsite.is_private ? '1' : '0'
        } : {}}
        onSubmit={handleUpdateWebsite}
        onIconSelect={handleIconSelect}
        selectedIcon={selectedIcon}
        loading={actionLoading}
      />

      {/* 删除确认模态框 */}
      <ConfirmDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setSelectedWebsite(null);
        }}
        title="删除网站"
        message={`确定要删除网站 "${selectedWebsite?.title}" 吗？此操作不可恢复。`}
        onConfirm={handleDeleteWebsite}
        loading={actionLoading}
      />
      
      {/* 图标获取进度弹窗 */}
      <IconFetchProgress
        isOpen={progressModal.isOpen}
        onClose={handleCloseProgressModal}
        onCancel={handleCancelIconFetch}
        website={progressModal.website}
        progress={progressModal.progress}
      />
    </div>
  );
}