import { NextRequest, NextResponse } from 'next/server';
import { query, queryOne } from '@/lib/database';
import { createApiResponse, createApiError, withAuth, withOptionalAuth } from '@/lib/utils';
import { generateCacheKey, withCache, invalidateCache, CACHE_TTL } from '@/lib/cache';

export const GET = withOptionalAuth(async (request: NextRequest & { user?: any }) => {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const isPrivate = searchParams.get('isPrivate');
    const sortBy = searchParams.get('sortBy') || 'order';
    const sortOrder = searchParams.get('sortOrder') || 'asc';
    
    // 获取用户信息
    const user = request.user;
    const isAuthenticated = !!user;
    const isSuperAdmin = user && user.role === 'super_admin';
    
    // 生成缓存键
    const cacheKey = generateCacheKey('categories', {
      page,
      limit,
      search,
      isPrivate,
      sortBy,
      sortOrder,
      userId: user?.id || 'anonymous',
      userRole: user?.role || 'anonymous'
    });
    
    // 使用缓存包装器
    const fetchCategoriesData = withCache(
      cacheKey,
      search ? CACHE_TTL.SHORT : CACHE_TTL.MEDIUM,
      async () => {
        const offset = (page - 1) * limit;
        
        let whereClause = 'WHERE 1=1';
        const params: any[] = [];
        
        // 权限控制逻辑
        if (!isAuthenticated) {
          whereClause += ' AND c.is_private = false';
        } else if (isSuperAdmin) {
          // 超级管理员：所有分类
        } else {
          // 登录非超级管理员：公开 + 本人私有分类 + “包含本人私有网站”的私有分类
          whereClause += ' AND (c.is_private = false OR (c.is_private = true AND (c.user_id = ? OR EXISTS (SELECT 1 FROM links l WHERE l.category_id = c.id AND l.is_private = true AND l.user_id = ?))))';
          params.push(user.id, user.id);
        }

        if (search) {
          whereClause += ' AND c.name LIKE ?';
          params.push(`%${search}%`);
        }
        
        if (isAuthenticated && isPrivate !== null) {
          whereClause += ' AND c.is_private = ?';
          params.push(isPrivate === 'true');
        }
        
        const orderByClause = `ORDER BY c.${sortBy} ${sortOrder.toUpperCase()}`;

        // 依据用户权限计算“可见链接数”的子查询与参数
        const paramsSelect: any[] = [];
        let linksCountSubquery = '(SELECT COUNT(*) FROM links WHERE category_id = c.id AND is_private = false)';
        if (isAuthenticated) {
          if (isSuperAdmin) {
            linksCountSubquery = '(SELECT COUNT(*) FROM links WHERE category_id = c.id)';
          } else {
            linksCountSubquery = '(SELECT COUNT(*) FROM links WHERE category_id = c.id AND (is_private = false OR (is_private = true AND user_id = ?)))';
            paramsSelect.push(user.id);
          }
        }

        const categoriesQuery = `
          SELECT
            c.id,
            c.name,
            c.order,
            c.icon_media_id,
            c.user_id,
            c.is_private,
            c.created_at,
            c.updated_at,
            u.username as created_by,
            ${linksCountSubquery} as links_count
          FROM categories c
          LEFT JOIN users u ON c.user_id = u.id
          ${whereClause}
          ${orderByClause}
          LIMIT ? OFFSET ?
        `;

        const countQuery = `
          SELECT COUNT(*) as total
          FROM categories c
          ${whereClause}
        `;

        const [categories, countResult] = await Promise.all([
          query(categoriesQuery, [...paramsSelect, ...params, limit, offset]),
          queryOne(countQuery, params)
        ]);

        const total = countResult.total;
        const totalPages = Math.ceil(total / limit);
        
        return {
          categories,
          pagination: {
            page,
            limit,
            total,
            totalPages
          }
        };
      }
    );
    
    const data = await fetchCategoriesData();
    return createApiResponse(true, data);
    
  } catch (error) {
    console.error('Get categories error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'Failed to fetch categories', error, 500);
  }
});

export const POST = withAuth(async (request: NextRequest & { user: any }) => {
  try {
    const body = await request.json();
    
    const { name, order, icon, isPrivate, parentId } = body;
    
    if (!name) {
      return createApiError('VALIDATION_ERROR', 'Category name is required', null, 400);
    }
    
    if (parentId) {
      const parentExists = await queryOne('SELECT id FROM categories WHERE id = ?', [parentId]);
      if (!parentExists) {
        return createApiError('CATEGORY_NOT_FOUND', 'Parent category not found', null, 404);
      }
    }
    
    const result = await query(
      'INSERT INTO categories (name, `order`, icon_media_id, user_id, is_private, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())',
      [name, order || 0, icon || null, request.user.id, isPrivate || false]
    );
    
    const newCategory = await queryOne(
      'SELECT id, name, `order`, icon_media_id, user_id, is_private, created_at, updated_at FROM categories WHERE id = ?',
      [(result as any).insertId]
    );
    
    // 缓存失效 - 清除相关缓存
    invalidateCache.categories();
    
    return createApiResponse(true, newCategory, 'Category created successfully', 201);
    
  } catch (error) {
    console.error('Create category error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'Failed to create category', error, 500);
  }
}, 'categories:create');