'use client';

import { useState, useEffect, useRef } from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import IconSelector, { IconSelection } from './IconSelector';

interface WebsiteModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  initialData?: {
    title?: string;
    url?: string;
    description?: string;
    category_id?: string;
    is_private?: string;
  };
  categoryOptions: { label: string; value: string }[];
  onSubmit: (data: Record<string, any>) => Promise<void>;
  onIconSelect: (icon: IconSelection) => void;
  selectedIcon: IconSelection;
  loading?: boolean;
  requireCategory?: boolean; // 新增：是否强制选择分类（用于新增）
}

export function WebsiteModal({
  isOpen,
  onClose,
  title,
  initialData = {},
  categoryOptions,
  onSubmit,
  onIconSelect,
  selectedIcon,
  loading = false,
  requireCategory = false
}: WebsiteModalProps) {
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [urlCheck, setUrlCheck] = useState<{ loading: boolean; reachable?: boolean; message?: string } | null>(null);

  // 可访问性检测的防抖函数（组件级作用域，供任意时机调用）
  const debounceTimerRef = useRef<any>(null);
  const debounceCheckUrl = (value: string) => {
    if (debounceTimerRef.current) clearTimeout(debounceTimerRef.current);
    const val = (value || '').trim();
    if (!val) { setUrlCheck(null); return; }
    setUrlCheck({ loading: true });
    debounceTimerRef.current = window.setTimeout(async () => {
      try {
        const res = await fetch('/api/utils/url-check', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ url: val })
        });
        if (res.ok) {
          const json = await res.json();
          const reachable = !!json?.data?.reachable;
          const status = json?.data?.status;
          const statusText = json?.data?.statusText || '';
          const extra = status ? `HTTP ${status}${statusText ? ' ' + statusText : ''}` : '';
          setUrlCheck({ loading: false, reachable, message: extra });
        } else {
          setUrlCheck({ loading: false, reachable: false, message: '检测失败' });
        }
      } catch (e: any) {
        setUrlCheck({ loading: false, reachable: false, message: e?.message || '网络错误' });
      }
    }, 500);
  };

  useEffect(() => {
    if (isOpen) {
      setFormData(initialData || {});
      setErrors({});
      const initialUrl = (initialData?.url || '').trim();
      if (initialUrl) {
        debounceCheckUrl(initialUrl);
      } else {
        setUrlCheck(null);
      }
    }
  }, [isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 基础验证
    const newErrors: Record<string, string> = {};

    if (!formData.title?.trim()) {
      newErrors.title = '网站标题是必填项';
    }

    if (!formData.url?.trim()) {
      newErrors.url = '网站地址是必填项';
    }

    // 分类必选（用于新增）
    if (requireCategory && !(`${formData.category_id || ''}`.trim())) {
      newErrors.category_id = '请选择分类后再保存';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const handleInputChange = (name: string, value: any) => {
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
    if (name === 'url') {
      debounceCheckUrl(value);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle>{title}</CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            disabled={loading}
          >
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* 网站标题 */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                网站标题 <span className="text-red-500 ml-1">*</span>
              </label>
              <Input
                type="text"
                value={formData.title || ''}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="请输入网站标题"
                disabled={loading}
              />
              {errors.title && (
                <p className="text-red-500 text-sm mt-1">{errors.title}</p>
              )}
            </div>

            {/* 网站地址 */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                网站地址 <span className="text-red-500 ml-1">*</span>
              </label>
              <div className="flex space-x-2">
                <Input
                  type="text"
                  value={formData.url || ''}
                  onChange={(e) => handleInputChange('url', e.target.value)}
                  placeholder="例如：https://example.com 或 example.com"
                  disabled={loading}
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    const val = (formData.url || '').trim();
                    if (!val) return;
                    const hasScheme = /^[a-zA-Z][a-zA-Z0-9+.-]*:\/\//.test(val);
                    const normalized = hasScheme ? val : `https://${val}`;
                    handleInputChange('url', normalized);
                  }}
                  disabled={loading || !(formData.url || '').trim()}
                  title="一键补全协议"
                >
                  补全
                </Button>
              </div>
              {errors.url && (
                <p className="text-red-500 text-sm mt-1">{errors.url}</p>
              )}
              {formData.url?.trim() && urlCheck && (
                <p className={`text-sm mt-1 ${urlCheck.loading ? 'text-muted-foreground' : urlCheck.reachable ? 'text-emerald-600' : 'text-amber-600'}`}>
                  {urlCheck.loading
                    ? '正在检测可访问性…'
                    : urlCheck.reachable
                      ? '可访问'
                      : (urlCheck.message ? `不可访问：${urlCheck.message}` : '不可访问')}
                </p>
              )}
            </div>

            {/* 网站描述 */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                网站描述
              </label>
              <textarea
                value={formData.description || ''}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="请输入网站描述（可选）"
                disabled={loading}
                rows={3}
                className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-foreground disabled:opacity-50 resize-none"
              />
            </div>

            {/* 分类选择 */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                分类{requireCategory && <span className="text-red-500 ml-1">*</span>}
              </label>
              <select
                value={formData.category_id || ''}
                onChange={(e) => handleInputChange('category_id', e.target.value)}
                disabled={loading}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-foreground disabled:opacity-50 ${errors.category_id ? 'border-red-500' : 'border-border'}`}
              >
                <option value="">请选择分类</option>
                {categoryOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.category_id && (
                <p className="text-red-500 text-sm mt-1">{errors.category_id}</p>
              )}
            </div>

            {/* 可见性设置 */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                可见性 <span className="text-red-500 ml-1">*</span>
              </label>
              <select
                value={formData.is_private || '0'}
                onChange={(e) => handleInputChange('is_private', e.target.value)}
                disabled={loading}
                className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-foreground disabled:opacity-50"
              >
                <option value="0">公开</option>
                <option value="1">私有</option>
              </select>
            </div>

            {/* 图标选择器 */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                网站图标
              </label>
              <IconSelector
                initialIcon={selectedIcon}
                websiteUrl={formData.url}
                websiteTitle={formData.title}
                onIconSelect={onIconSelect}
                className="bg-background"
              />
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={loading}
              >
                取消
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? '保存中...' : '保存'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}