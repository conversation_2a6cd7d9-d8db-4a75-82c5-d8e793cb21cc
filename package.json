{"name": "navigation-website-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "dev:3004": "next dev -p 3004", "build": "next build", "start": "next start -p 3000", "lint": "next lint", "clean": "rm -rf .next && rm -rf node_modules/.cache", "kill-ports": "npx kill-port 3000 3004 3010"}, "dependencies": {"@radix-ui/react-slot": "^1.0.2", "@types/react-window": "^1.8.8", "bcrypt": "^5.1.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^4.1.0", "framer-motion": "^11.2.0", "jose": "^5.1.0", "jsonwebtoken": "^9.0.0", "lucide-react": "^0.468.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.0", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "recharts": "^3.1.0", "sharp": "^0.33.0", "swr": "^2.3.4", "tailwind-merge": "^2.0.0", "uuid": "^9.0.0", "zod": "^3.22.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcrypt": "^5.0.0", "@types/jsonwebtoken": "^9.0.0", "@types/multer": "^1.4.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^9.0.0", "eslint": "^9", "eslint-config-next": "15.4.5", "kill-port": "^2.0.1", "tailwindcss": "^4", "typescript": "^5"}}