'use client';

import { useState } from 'react';
import { Folder<PERSON>pen, ChevronRight } from 'lucide-react';

interface Category {
  id: number;
  name: string;
  order: number;
  is_private: boolean;
  links_count: number;
}

interface CategoryFilterProps {
  categories: Category[];
  onCategorySelect?: (categoryId: number | null) => void;
}

export default function CategoryFilter({ categories, onCategorySelect }: CategoryFilterProps) {
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);

  const handleCategoryClick = (categoryId: number | null) => {
    setSelectedCategory(categoryId);
    if (onCategorySelect) {
      onCategorySelect(categoryId);
    }
  };

  // 确保 categories 是数组
  const categoriesArray = Array.isArray(categories) ? categories : [];

  return (
    <div className="bg-card border border-border rounded-lg p-4 sm:p-6">
      <div className="flex items-center space-x-2 mb-3 sm:mb-4">
        <FolderOpen className="w-4 h-4 sm:w-5 sm:h-5 text-primary" />
        <h2 className="text-base sm:text-lg font-semibold text-foreground">
          分类浏览
        </h2>
      </div>
      
      <div className="flex flex-wrap gap-2">
        <button
          onClick={() => handleCategoryClick(null)}
          className={`inline-flex items-center space-x-1.5 sm:space-x-2 px-3 py-1.5 sm:px-4 sm:py-2 rounded-lg text-xs sm:text-sm font-medium transition-colors ${
            selectedCategory === null
              ? 'bg-primary text-primary-foreground'
              : 'bg-muted text-muted-foreground hover:bg-muted/80'
          }`}
        >
          <span>全部</span>
          <span className="text-xs opacity-70">
            {categoriesArray.reduce((total, cat) => total + (cat.links_count || 0), 0)}
          </span>
        </button>
        
        {categoriesArray
          .sort((a, b) => (a.order || 0) - (b.order || 0))
          .map((category) => (
            <button
              key={category.id}
              onClick={() => handleCategoryClick(category.id)}
              className={`inline-flex items-center space-x-1.5 sm:space-x-2 px-3 py-1.5 sm:px-4 sm:py-2 rounded-lg text-xs sm:text-sm font-medium transition-colors ${
                selectedCategory === category.id
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-muted text-muted-foreground hover:bg-muted/80'
              }`}
            >
              {Boolean(category.is_private) && (
                <span className="text-[10px] px-1 py-0.5 rounded border border-border text-muted-foreground mr-1">私有</span>
              )}
              <span>{category.name}</span>
              {(category.links_count ?? 0) > 0 && (
                <span className="text-xs opacity-70">{category.links_count}</span>
              )}
            </button>
          ))}
      </div>
      
      {selectedCategory !== null && (
        <div className="mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-border">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
            <span className="text-xs sm:text-sm text-muted-foreground">
              已选择分类筛选
            </span>
            <button
              onClick={() => handleCategoryClick(null)}
              className="text-xs sm:text-sm text-primary hover:text-primary/80 transition-colors self-start sm:self-auto"
            >
              清除筛选
            </button>
          </div>
        </div>
      )}
    </div>
  );
}