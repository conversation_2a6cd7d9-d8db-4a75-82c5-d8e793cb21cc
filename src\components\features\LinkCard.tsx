import Link from 'next/link';
import Image from 'next/image';
import { ExternalLink, Calendar, Tag, Star } from 'lucide-react';
import { useState, useEffect } from 'react';
import { formatDateSafe } from '@/lib/utils/dateUtils';

interface WebsiteCardProps {
  link: {
    id: number;
    title: string;
    url: string;
    description?: string;
    category_name: string;
    icon_media_id?: number;
    created_at: string;
  };
}

interface WebsiteIconProps {
  link: WebsiteCardProps['link'];
  className: string;
}

// 网站图标组件
function WebsiteIcon({ link, className }: WebsiteIconProps) {
  const [imageError, setImageError] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const getFaviconUrl = (url: string) => {
    try {
      const domain = new URL(url).hostname;
      return `https://www.google.com/s2/favicons?domain=${domain}&sz=64`;
    } catch {
      return null;
    }
  };

  const getInitials = (title: string) => {
    return title.charAt(0).toUpperCase();
  };

  const getBackgroundColor = (title: string) => {
    const colors = [
      'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500',
      'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500',
      'bg-orange-500', 'bg-cyan-500', 'bg-lime-500', 'bg-rose-500'
    ];
    
    const index = title.charCodeAt(0) % colors.length;
    return colors[index];
  };

  if (!mounted) {
    return (
      <div className={`${className} rounded-lg bg-muted animate-pulse`} />
    );
  }

  // 如果有图标ID，尝试显示自定义图标
  const customIconUrl = link.icon_media_id ? `/uploads/icons/${link.icon_media_id}` : null;
  const faviconUrl = getFaviconUrl(link.url);

  if (customIconUrl && !imageError) {
    return (
      <Image
        src={customIconUrl}
        alt={link.title}
        width={48}
        height={48}
        className={`${className} rounded-lg object-cover`}
        onError={() => setImageError(true)}
        priority={false}
        loading="lazy"
      />
    );
  }

  if (faviconUrl && !imageError) {
    return (
      <Image
        src={faviconUrl}
        alt={link.title}
        width={48}
        height={48}
        className={`${className} rounded-lg object-cover bg-white`}
        onError={() => setImageError(true)}
        priority={false}
        loading="lazy"
        unoptimized // Google favicons需要跳过优化
      />
    );
  }

  // Fallback到字母头像
  return (
    <div className={`${className} rounded-lg flex items-center justify-center text-white font-semibold text-sm sm:text-lg ${getBackgroundColor(link.title)}`}>
      {getInitials(link.title)}
    </div>
  );
}

export default function LinkCard({ link }: WebsiteCardProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const formatDate = (dateString: string) => {
    if (!mounted) return '加载中...';
    return formatDateSafe(dateString);
  };

  const normalizeHref = (url: string) => {
    try {
      // 有协议直接返回
      const u = new URL(url);
      return u.toString();
    } catch {
      // 无协议时补全 https://
      if (!url) return '#';
      return `https://${url}`;
    }
  };

  return (
    <Link
      href={normalizeHref(link.url)}
      target="_blank"
      rel="noopener noreferrer"
      className="group card-modern block cursor-pointer overflow-hidden"
    >
      {/* 卡片头部 */}
      <div className="relative p-6 pb-4">
        {/* 背景装饰 */}
        <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-primary/10 to-transparent rounded-bl-3xl"></div>

        <div className="relative flex items-start space-x-4">
          <div className="flex-shrink-0 relative">
            <WebsiteIcon
              link={link}
              className="w-14 h-14 shadow-lg"
            />
            {/* 图标装饰环 */}
            <div className="absolute -inset-1 bg-gradient-to-r from-primary/20 to-primary/10 rounded-full blur-sm group-hover:blur-md transition-all duration-300"></div>
          </div>

          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-bold text-foreground mb-1 group-hover:text-primary transition-colors duration-300 line-clamp-1">
              {link.title}
            </h3>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Tag className="w-3 h-3" />
              <span className="truncate">{link.category_name}</span>
            </div>
          </div>
        </div>
      </div>

      {/* 卡片内容 */}
      <div className="px-6 pb-4">
        {link.description && (
          <p className="text-sm text-muted-foreground mb-4 line-clamp-2 leading-relaxed">
            {link.description}
          </p>
        )}
      </div>

      {/* 卡片底部 */}
      <div className="px-6 py-4 bg-muted/20 border-t border-border/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm font-medium text-primary group-hover:text-primary/80 transition-colors">
            <ExternalLink className="w-4 h-4" />
            <span>访问网站</span>
          </div>

          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Calendar className="w-3 h-3" />
            <span>{formatDate(link.created_at)}</span>
          </div>
        </div>
      </div>

      {/* 悬停效果指示器 */}
      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-primary to-primary/60 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
    </Link>
  );
}