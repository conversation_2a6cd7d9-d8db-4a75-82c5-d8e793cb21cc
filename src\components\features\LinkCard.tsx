import Link from 'next/link';
import Image from 'next/image';
import { ExternalLink } from 'lucide-react';
import { useState, useEffect } from 'react';
import { formatDateSafe } from '@/lib/utils/dateUtils';

interface WebsiteCardProps {
  link: {
    id: number;
    title: string;
    url: string;
    description?: string;
    category_name: string;
    icon_media_id?: number;
    created_at: string;
  };
}

interface WebsiteIconProps {
  link: WebsiteCardProps['link'];
  className: string;
}

// 网站图标组件
function WebsiteIcon({ link, className }: WebsiteIconProps) {
  const [imageError, setImageError] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const getFaviconUrl = (url: string) => {
    try {
      const domain = new URL(url).hostname;
      return `https://www.google.com/s2/favicons?domain=${domain}&sz=64`;
    } catch {
      return null;
    }
  };

  const getInitials = (title: string) => {
    return title.charAt(0).toUpperCase();
  };

  const getBackgroundColor = (title: string) => {
    const colors = [
      'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500',
      'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500',
      'bg-orange-500', 'bg-cyan-500', 'bg-lime-500', 'bg-rose-500'
    ];
    
    const index = title.charCodeAt(0) % colors.length;
    return colors[index];
  };

  if (!mounted) {
    return (
      <div className={`${className} rounded-lg bg-muted animate-pulse`} />
    );
  }

  // 如果有图标ID，尝试显示自定义图标
  const customIconUrl = link.icon_media_id ? `/uploads/icons/${link.icon_media_id}` : null;
  const faviconUrl = getFaviconUrl(link.url);

  if (customIconUrl && !imageError) {
    return (
      <Image
        src={customIconUrl}
        alt={link.title}
        width={48}
        height={48}
        className={`${className} rounded-lg object-cover`}
        onError={() => setImageError(true)}
        priority={false}
        loading="lazy"
      />
    );
  }

  if (faviconUrl && !imageError) {
    return (
      <Image
        src={faviconUrl}
        alt={link.title}
        width={48}
        height={48}
        className={`${className} rounded-lg object-cover bg-white`}
        onError={() => setImageError(true)}
        priority={false}
        loading="lazy"
        unoptimized // Google favicons需要跳过优化
      />
    );
  }

  // Fallback到字母头像
  return (
    <div className={`${className} rounded-lg flex items-center justify-center text-white font-semibold text-sm sm:text-lg ${getBackgroundColor(link.title)}`}>
      {getInitials(link.title)}
    </div>
  );
}

export default function LinkCard({ link }: WebsiteCardProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const formatDate = (dateString: string) => {
    if (!mounted) return '加载中...';
    return formatDateSafe(dateString);
  };

  const normalizeHref = (url: string) => {
    try {
      // 有协议直接返回
      const u = new URL(url);
      return u.toString();
    } catch {
      // 无协议时补全 https://
      if (!url) return '#';
      return `https://${url}`;
    }
  };

  return (
    <Link
      href={normalizeHref(link.url)}
      target="_blank"
      rel="noopener noreferrer"
      className="group bg-card border border-border rounded-lg p-4 sm:p-6 hover:shadow-lg transition-all duration-200 hover:scale-105 block cursor-pointer"
    >
      <div className="flex items-start space-x-3 sm:space-x-4 mb-3 sm:mb-4">
        <div className="flex-shrink-0 relative">
          <WebsiteIcon
            link={link}
            className="w-10 h-10 sm:w-12 sm:h-12"
          />
        </div>
        
        <div className="flex-1 min-w-0">
          <h3 className="text-base sm:text-lg font-semibold text-foreground truncate group-hover:text-primary transition-colors">
            {link.title}
          </h3>
          <p className="text-xs sm:text-sm text-muted-foreground">
            {link.category_name}
          </p>
        </div>
      </div>

      {link.description && (
        <p className="text-xs sm:text-sm text-muted-foreground mb-3 sm:mb-4 line-clamp-2">
          {link.description}
        </p>
      )}

      <div className="flex items-center justify-between">
        <div className="inline-flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm text-primary group-hover:text-primary/80 transition-colors">
          <span>访问网站</span>
          <ExternalLink className="w-3 h-3 sm:w-4 sm:h-4" />
        </div>
        
        <span className="text-xs text-muted-foreground">
          {formatDate(link.created_at)}
        </span>
      </div>
    </Link>
  );
}