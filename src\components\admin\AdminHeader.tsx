'use client';

import { useEffect, useRef, useState } from 'react';
import Link from 'next/link';
import { Bell, Search, User, LogOut, Menu, Settings, Home } from 'lucide-react';

interface AdminHeaderProps {
  user: {
    id: number;
    username: string;
    email: string;
    role: string;
  };
  onMenuClick: () => void;
}

export default function AdminHeader({ user, onMenuClick }: AdminHeaderProps) {
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const userMenuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const onDocClick = (e: MouseEvent) => {
      if (!userMenuRef.current) return;
      const target = e.target as Node;
      // 如果点击不在用户按钮/菜单容器内，则关闭菜单
      if (!userMenuRef.current.contains(target)) {
        setIsUserMenuOpen(false);
      }
    };
    document.addEventListener('mousedown', onDocClick);
    return () => document.removeEventListener('mousedown', onDocClick);
  }, []);

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
      });
      window.location.href = '/admin/login';
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-md border-b border-border/50 shadow-lg">
      <div className="flex items-center justify-between px-3 sm:px-6 py-3 sm:py-4">
        <div className="flex items-center space-x-2 sm:space-x-4">
          <button 
            onClick={onMenuClick}
            className="p-2 rounded-lg hover:bg-muted transition-colors lg:hidden"
          >
            <Menu className="w-5 h-5" />
          </button>
          
          <h1 className="text-sm sm:text-lg lg:text-xl font-semibold text-foreground truncate">
            <span className="hidden sm:inline">资源导航管理系统</span>
            <span className="sm:hidden">管理系统</span>
          </h1>
        </div>

        <div className="flex items-center space-x-1 sm:space-x-4">
          <Link 
            href="/"
            className="flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3 py-2 text-xs sm:text-sm text-muted-foreground hover:text-primary transition-all duration-200 rounded-lg hover:bg-muted/50"
            title="返回前台首页"
          >
            <Home className="w-4 h-4" />
            <span className="hidden lg:block">返回首页</span>
          </Link>
          
          <div className="hidden sm:block h-4 w-px bg-border"></div>
          
          <div className={`relative ${isSearchOpen ? 'fixed inset-x-0 top-16 p-4 bg-background/95 backdrop-blur-md border-b border-border z-50' : 'hidden lg:block'}`}>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="搜索..."
                className="w-full lg:w-64 pl-10 pr-4 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
          </div>

          <button
            onClick={() => setIsSearchOpen(!isSearchOpen)}
            className="p-2 rounded-lg hover:bg-muted transition-colors lg:hidden"
          >
            <Search className="w-4 h-4 sm:w-5 sm:h-5" />
          </button>

          <button className="hidden sm:block p-2 rounded-lg hover:bg-muted transition-colors relative">
            <Bell className="w-4 h-4 sm:w-5 sm:h-5" />
            <span className="absolute top-1 right-1 w-2 h-2 bg-destructive rounded-full"></span>
          </button>

          <div className="relative" ref={userMenuRef}>
            <button
              onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
              className="flex items-center space-x-1 sm:space-x-2 p-1 sm:p-2 rounded-lg hover:bg-muted transition-colors"
            >
              <div className="w-6 h-6 sm:w-8 sm:h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-xs sm:text-sm font-semibold">
                {user.username.charAt(0).toUpperCase()}
              </div>
              <span className="hidden md:block text-sm font-medium text-foreground max-w-20 truncate">
                {user.username}
              </span>
            </button>

            {isUserMenuOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-background/90 backdrop-blur-lg border border-border/50 rounded-lg shadow-xl py-1 z-50 animate-in slide-in-from-top-1 duration-200">
                <div className="px-4 py-2 border-b border-border/50">
                  <p className="text-sm font-medium text-foreground truncate">{user.username}</p>
                  <p className="text-xs text-muted-foreground truncate">{user.email}</p>
                  <p className="text-xs text-muted-foreground">{user.role}</p>
                </div>
                
                <Link
                  href="/profile"
                  className="block px-4 py-2 text-sm text-muted-foreground hover:bg-muted/80 hover:text-foreground transition-all duration-200"
                >
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4" />
                    <span>个人资料</span>
                  </div>
                </Link>
                
<div className="border-t border-border/50 mt-1 pt-1">
                  <button
                    onClick={handleLogout}
                    className="block w-full text-left px-4 py-2 text-sm text-muted-foreground hover:bg-muted/80 hover:text-foreground transition-all duration-200"
                  >
                    <div className="flex items-center space-x-2">
                      <LogOut className="w-4 h-4" />
                      <span>退出登录</span>
                    </div>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}