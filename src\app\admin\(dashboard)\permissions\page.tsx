import { query, queryOne } from '@/lib/database';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Plus, Edit, Trash2, Shield, Users } from 'lucide-react';
import PermissionsManagerClient from './PermissionsManagerClient';
import { cookies } from 'next/headers';
import { verifyJWT } from '@/lib/utils';

// 权限名称中文映射
const getPermissionDisplayName = (permissionName: string): string => {
  const permissionMap: Record<string, string> = {
    // 管理员权限
    'admin:access': '管理员访问',
    'admin:dashboard:view': '查看仪表盘',
    
    // 用户管理权限
    'users:list': '用户列表',
    'users:create': '创建用户',
    'users:edit': '编辑用户',
    'users:update': '更新用户',
    'users:delete': '删除用户',
    'users:read': '查看用户',
    'users:view': '查看用户',
    'users:manage': '用户管理',
    
    // 角色管理权限
    'roles:list': '角色列表',
    'roles:create': '创建角色',
    'roles:edit': '编辑角色',
    'roles:update': '更新角色',
    'roles:delete': '删除角色',
    'roles:view': '查看角色',
    'roles:manage': '角色管理',
    
    // 分类管理权限
    'categories:list': '分类列表',
    'categories:create': '创建分类',
    'categories:edit': '编辑分类',
    'categories:update': '更新分类',
    'categories:delete': '删除分类',
    'categories:read': '查看分类',
    'categories:view': '查看分类',
    'categories:manage': '分类管理',
    
    // 链接管理权限
    'links:list': '链接列表',
    'links:create': '创建链接',
    'links:edit': '编辑链接',
    'links:update': '更新链接',
    'links:delete': '删除链接',
    'links:read': '查看链接',
    'links:view': '查看链接',
    'links:manage': '链接管理',
    
    // 日志权限
    'logs:view': '查看日志',
    'logs:read': '查看日志',
    
    // 系统管理权限
    'system:settings': '系统设置',
    'system:backup': '数据备份',
    'system:logs': '系统日志',
    'system:manage': '系统管理',
    
    // 内容管理权限
    'content:view': '查看内容',
    'content:create': '创建内容',
    'content:update': '编辑内容',
    'content:delete': '删除内容',
    'content:publish': '发布内容',
    
    // 个人资料权限
    'profile:view': '查看资料',
    'profile:edit': '编辑资料',
    'profile:password': '修改密码',
    
    // 统计报表权限
    'analytics:view': '查看统计',
    'analytics:export': '导出数据',
    
    // 媒体管理权限
    'media:view': '查看媒体',
    'media:upload': '上传媒体',
    'media:delete': '删除媒体'
  };
  
  return permissionMap[permissionName] || permissionName;
};

async function getRoles() {
  try {
    const roles = await query(`
      SELECT 
        r.id,
        r.name,
        COUNT(DISTINCT rp.permission_id) as permission_count,
        COUNT(DISTINCT u.id) as user_count
      FROM roles r
      LEFT JOIN role_permissions rp ON r.id = rp.role_id
      LEFT JOIN users u ON r.id = u.role_id
      GROUP BY r.id, r.name
      ORDER BY r.id
    `);
    
    return roles;
  } catch (error) {
    console.error('Error fetching roles:', error);
    return [];
  }
}

async function getPermissions() {
  try {
    const permissions = await query(`
      SELECT id, name
      FROM permissions
      ORDER BY name
    `);
    
    return permissions;
  } catch (error) {
    console.error('Error fetching permissions:', error);
    return [];
  }
}

async function getRolePermissions(roleId: number) {
  try {
    const permissions = await query(`
      SELECT p.id, p.name
      FROM permissions p
      JOIN role_permissions rp ON p.id = rp.permission_id
      WHERE rp.role_id = ?
      ORDER BY p.name
    `, [roleId]);
    
    return permissions;
  } catch (error) {
    console.error('Error fetching role permissions:', error);
    return [];
  }
}

export default async function PermissionsPage() {
  const [roles, permissions] = await Promise.all([getRoles(), getPermissions()]);

  // 获取当前用户的角色与权限（来自JWT），用于隔离展示
  const cookieStore = await cookies();
  const token = cookieStore.get('token')?.value;
  let payload: any = null;
  try { if (token) payload = await verifyJWT(token); } catch {}
  const isSuperAdmin = payload?.role === 'super_admin';
  const currentRole = payload?.role || '';
  const currentPermissions: string[] = Array.isArray(payload?.permissions) ? payload.permissions : [];

  const rolesWithPermissions = await Promise.all(
    roles.map(async (role) => ({
      ...role,
      permissions: await getRolePermissions(role.id)
    }))
  );

  // 过滤：非超级管理员仅能看到自己“可分配/可操作”的权限与角色
  const filteredPermissions = isSuperAdmin ? permissions : permissions.filter((p: any) => currentPermissions.includes(p.name));
  const displayName = (name: string) => getPermissionDisplayName(name);
  const filteredRolesWithPermissions = isSuperAdmin
    ? rolesWithPermissions
    : rolesWithPermissions.filter((role: any) => role.name !== 'super_admin' && role.permissions.every((p: any) => currentPermissions.includes(p.name)));

  return (
    <div>
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-foreground mb-2">
            权限管理
          </h1>
          <p className="text-muted-foreground">
            管理用户角色和权限设置
          </p>
        </div>
        {/* 客户端能力接管“新增/编辑/删除角色”交互，仅对超级管理员呈现 */}
        <PermissionsManagerClient isSuperAdmin={isSuperAdmin} roles={filteredRolesWithPermissions} permissions={filteredPermissions} />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="w-5 h-5" />
              <span>角色列表</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredRolesWithPermissions.map((role) => (
                <div key={role.id} className="border border-border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-sm font-semibold">
                        {role.name.charAt(0).toUpperCase()}
                      </div>
                      <div>
                        <h3 className="font-medium text-foreground">
                          {role.name === 'super_admin' ? '超级管理员' : 
                           role.name === 'admin' ? '管理员' : '用户'}
                        </h3>
                        <div className="flex items-center space-x-4 mt-1">
                          <span className="text-sm text-muted-foreground">
                            {role.user_count} 个用户
                          </span>
                          <span className="text-sm text-muted-foreground">
                            {role.permission_count} 个权限
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {/* 角色项操作由客户端组件接管，这里仅展示数据 */}
                    </div>
                  </div>
                  
                  <div className="mt-3">
                    <div className="flex flex-wrap gap-2">
                      {role.permissions.map((permission) => (
                        <Badge key={permission.id} variant="outline" className="text-xs">
                          {displayName(permission.name)}
                        </Badge>
                      ))}
                      {role.permissions.length === 0 && (
                        <span className="text-sm text-muted-foreground">暂无权限</span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="w-5 h-5" />
              <span>权限列表</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {filteredPermissions.map((permission) => (
                <div key={permission.id} className="flex items-center justify-between p-3 border border-border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
                      <Shield className="w-4 h-4 text-muted-foreground" />
                    </div>
                    <div>
                      <p className="font-medium text-foreground text-sm">
                        {getPermissionDisplayName(permission.name)}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {permission.name}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-xs">
                      {rolesWithPermissions.filter(role => 
                        role.permissions.some(p => p.id === permission.id)
                      ).length} 个角色
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 权限分配建议模块已移除，避免误导与信息泄露 */}

    </div>
  );
}