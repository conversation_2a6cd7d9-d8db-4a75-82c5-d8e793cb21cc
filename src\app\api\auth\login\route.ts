import { NextRequest, NextResponse } from 'next/server';
import { SignJWT } from 'jose';
import { query, queryOne } from '@/lib/database';
import { signJWT, createApiResponse, createApiError } from '@/lib/utils';
import { verifyPassword } from '@/lib/utils/server';
import { loginSchema } from '@/lib/validation';

const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || 'your-secret-key');

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const validatedData = loginSchema.parse(body);
    const { username, password, remember } = validatedData;
    
    const user = await queryOne(
      `SELECT 
        u.id,
        u.username,
        u.email,
        u.password,
        u.role_id,
        r.name as role,
        u.avatar_media_id,
        u.created_at,
        u.updated_at
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.id
      WHERE u.username = ? OR u.email = ?`,
      [username, username]
    );
    
    if (!user) {
      return createApiError('INVALID_CREDENTIALS', '用户名或密码错误', null, 401);
    }
    
    const isValidPassword = await verifyPassword(password, user.password);
    if (!isValidPassword) {
      return createApiError('INVALID_CREDENTIALS', '用户名或密码错误', null, 401);
    }
    
    const permissions = await query(
      `SELECT p.name
       FROM permissions p
       JOIN role_permissions rp ON p.id = rp.permission_id
       WHERE rp.role_id = ?`,
      [user.role_id]
    );
    
    const token = await signJWT(
      {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        permissions: permissions.map(p => p.name)
      },
      remember ? '7d' : '1d'
    );
    
    await query(
      'INSERT INTO activity_logs (user_id, action, target_type, target_id, created_at) VALUES (?, ?, ?, ?, NOW())',
      [user.id, '用户登录', 'user', user.id]
    );
    
    const responseData = {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        avatar: user.avatar_media_id,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      },
      token,
      permissions: permissions.map(p => p.name)
    };
    
    const response = createApiResponse(true, responseData, '登录成功');
    
    response.cookies.set('token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: remember ? 7 * 24 * 60 * 60 : 24 * 60 * 60,
      path: '/'
    });
    
    return response;
    
  } catch (error) {
    console.error('Login error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', '登录失败', error, 500);
  }
}