@import "tailwindcss";

:root {
  /* 现代化色彩系统 */
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;
  --primary: 263 70% 50.4%;
  --primary-foreground: 0 0% 98%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --accent: 240 3.7% 15.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --ring: 263 70% 50.4%;
  --radius: 0.75rem;

  /* 现代化渐变色 */
  --gradient-primary: linear-gradient(135deg, #6366f1 0%, #06b6d4 100%);
  --gradient-secondary: linear-gradient(135deg, #f59e0b 0%, #ef4444 100%);
  --gradient-accent: linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%);

  /* 动画变量 */
  --animation-duration: 0.3s;
  --animation-timing: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 亮色主题 */
.light {
  --background: 0 0% 100%;
  --foreground: 240 10% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 240 10% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 240 10% 3.9%;
  --primary: 263 70% 50.4%;
  --primary-foreground: 0 0% 98%;
  --secondary: 240 4.8% 95.9%;
  --secondary-foreground: 240 5.9% 10%;
  --muted: 240 4.8% 95.9%;
  --muted-foreground: 240 3.8% 46.1%;
  --accent: 240 4.8% 95.9%;
  --accent-foreground: 240 5.9% 10%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --border: 240 5.9% 90%;
  --input: 240 5.9% 90%;
  --ring: 263 70% 50.4%;
}

@theme inline {
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));
  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));
  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));
  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));
  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));
  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));
  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --radius: var(--radius);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

* {
  border-color: hsl(var(--border));
}

body {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  font-size: 16px;
  line-height: 1.6;
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* 现代化滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--primary));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.8);
}

/* 移动端触摸优化 */
* {
  -webkit-tap-highlight-color: transparent;
}

/* 按钮和链接的最小触摸目标 */
button, a, [role="button"] {
  min-height: 44px;
}

/* 移动端输入框优化 */
input, textarea, select {
  font-size: 16px; /* 防止iOS放大页面 */
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* 响应式容器优化 */
@media (max-width: 640px) {
  .container {
    padding: 0 1rem;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 769px) {
  .container {
    padding: 0 2rem;
  }
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.grid {
  display: grid;
  gap: 1.5rem;
}

.gap-6 {
  gap: 1.5rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.border {
  border-width: 1px;
}

.border-border {
  border-color: hsl(var(--border));
}

.bg-card {
  background-color: hsl(var(--card));
}

.bg-background {
  background-color: hsl(var(--background));
}

.bg-muted {
  background-color: hsl(var(--muted));
}

.bg-primary {
  background-color: hsl(var(--primary));
}

.text-foreground {
  color: hsl(var(--foreground));
}

.text-muted-foreground {
  color: hsl(var(--muted-foreground));
}

.text-primary {
  color: hsl(var(--primary));
}

.text-primary-foreground {
  color: hsl(var(--primary-foreground));
}

.hover\:bg-primary\/90:hover {
  background-color: hsl(var(--primary) / 0.9);
}

.hover\:bg-muted\/80:hover {
  background-color: hsl(var(--muted) / 0.8);
}

.hover\:text-primary:hover {
  color: hsl(var(--primary));
}

.hover\:text-primary\/80:hover {
  color: hsl(var(--primary) / 0.8);
}

.hover\:text-foreground:hover {
  color: hsl(var(--foreground));
}

.hover\:shadow-lg:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.font-semibold {
  font-weight: 600;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.font-bold {
  font-weight: 700;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pl-4 {
  padding-left: 1rem;
}

.pr-4 {
  padding-right: 1rem;
}

.pr-10 {
  padding-right: 2.5rem;
}

.pl-12 {
  padding-left: 3rem;
}

.w-full {
  width: 100%;
}

.w-5 {
  width: 1.25rem;
}

.w-12 {
  width: 3rem;
}

.w-4 {
  width: 1rem;
}

.h-5 {
  height: 1.25rem;
}

.h-12 {
  height: 3rem;
}

.h-4 {
  height: 1rem;
}

.min-w-0 {
  min-width: 0;
}

.min-h-screen {
  min-height: 100vh;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.justify-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.border {
  border-width: 1px;
}

.border-border {
  border-color: hsl(var(--border));
}

.border-t {
  border-top-width: 1px;
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-2:focus {
  box-shadow: 0 0 0 2px var(--tw-ring-color);
}

.focus\:ring-primary:focus {
  --tw-ring-color: hsl(var(--primary));
}

.focus\:border-transparent:focus {
  border-color: transparent;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.inset-y-0 {
  top: 0;
  bottom: 0;
}

.left-0 {
  left: 0;
}

.right-0 {
  right: 0;
}

.top-full {
  top: 100%;
}

.z-10 {
  z-index: 10;
}

.pointer-events-none {
  pointer-events: none;
}

.overflow-hidden {
  overflow: hidden;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.opacity-70 {
  opacity: 0.7;
}

.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.object-cover {
  object-fit: cover;
}

.flex-1 {
  flex: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.group\/item {
  container-type: inline-size;
}

.hover\:scale-105:hover {
  transform: scale(1.05);
}

.hover\:shadow-lg:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.hover\:bg-muted\/80:hover {
  background-color: hsl(var(--muted) / 0.8);
}

.hover\:text-primary:hover {
  color: hsl(var(--primary));
}

.hover\:text-primary\/80:hover {
  color: hsl(var(--primary) / 0.8);
}

.hover\:text-foreground:hover {
  color: hsl(var(--foreground));
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 1280px) {
  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 0.5rem;
  }
}

/* 现代化动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes gradient {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* 现代化组件样式 */
.hero-gradient {
  background: var(--gradient-primary);
  background-size: 400% 400%;
  animation: gradient 15s ease infinite;
}

.card-modern {
  background: hsl(var(--card) / 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid hsl(var(--border) / 0.5);
  border-radius: var(--radius);
  transition: all var(--animation-duration) var(--animation-timing);
}

.card-modern:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px -12px hsl(var(--primary) / 0.25);
  border-color: hsl(var(--primary) / 0.5);
}

.glass-effect {
  background: hsl(var(--background) / 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid hsl(var(--border) / 0.2);
}

.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.btn-modern {
  background: var(--gradient-primary);
  border: none;
  border-radius: var(--radius);
  color: white;
  font-weight: 600;
  padding: 12px 24px;
  transition: all var(--animation-duration) var(--animation-timing);
  position: relative;
  overflow: hidden;
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn-modern:hover::before {
  left: 100%;
}

.btn-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px hsl(var(--primary) / 0.3);
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-scale {
  animation: fadeInScale 0.5s ease-out;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 搜索栏现代化样式 */
.search-modern {
  background: hsl(var(--background) / 0.9);
  backdrop-filter: blur(20px);
  border: 2px solid hsl(var(--border) / 0.3);
  border-radius: 50px;
  padding: 16px 24px;
  font-size: 16px;
  transition: all var(--animation-duration) var(--animation-timing);
  width: 100%;
  max-width: 600px;
}

.search-modern:focus {
  outline: none;
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 4px hsl(var(--primary) / 0.1);
  transform: scale(1.02);
}

/* 分类标签现代化样式 */
.category-tag {
  background: hsl(var(--muted) / 0.5);
  backdrop-filter: blur(10px);
  border: 1px solid hsl(var(--border) / 0.3);
  border-radius: 25px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  transition: all var(--animation-duration) var(--animation-timing);
  cursor: pointer;
}

.category-tag:hover {
  background: hsl(var(--primary) / 0.1);
  border-color: hsl(var(--primary) / 0.5);
  transform: translateY(-2px);
}

.category-tag.active {
  background: var(--gradient-primary);
  color: white;
  border-color: transparent;
}

/* 响应式优化 */
@media (max-width: 640px) {
  .hero-gradient {
    background-size: 200% 200%;
  }

  .card-modern:hover {
    transform: translateY(-4px) scale(1.01);
  }

  .search-modern {
    padding: 12px 20px;
    font-size: 16px;
  }
}
