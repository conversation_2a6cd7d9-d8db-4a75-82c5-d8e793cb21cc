# 资源导航网站需求文档

## 项目概述
开发一个资源导航网站，提供各类网站资源的分类展示和管理功能。网站面向游客和管理员提供不同级别的访问权限。

## 功能需求

### 1. 用户系统
#### 1.1 用户角色
- **游客**：可浏览和访问公开资源
- **普通管理员**：可管理网站资源
- **超级管理员**：拥有最高权限，可管理所有管理员和系统设置

#### 1.2 用户功能
- 注册登录功能
- 修改个人信息功能
- 权限控制和访问限制

### 2. 资源管理系统
#### 2.1 资源分类
- 支持多级分类管理
- 后台可手动调整分类排序
- 影响首页展示顺序
- 批量操作功能

#### 2.2 资源添加和编辑
- 管理员可添加、编辑、删除网站
- 批量操作功能
- 添加、编辑网站有自动生成图标，上传图标的功能（使用高效算法自动生成，首字母加相应的背景色，背景色不能相同）
- 支持在导航页直接添加资源（快捷添加功能）
- 无需返回设置页面进行编辑

#### 2.3 数据权限
- **公开数据**：所有用户可见
- **隐私数据**：仅发表的用户、管理员可见

### 3. 超级管理员功能
#### 3.1 管理员管理
- 添加管理员账号
- 删除管理员账号
- 细致的权限设置

#### 3.2 日志记录系统
- 记录操作日志
- 登录时间记录
- 操作行为追踪
- 查看特定管理员的操作历史

### 4. 前端展示功能
- 瀑布流布局展示资源
- 分类导航展示
- 响应式设计

- 引入动画库增强用户体验
- 集成 Three.js 实现3D效果
- 自由发挥优雅的UI设计
- 炫酷的交互动效

- 所有列表页面支持分页
- 优化加载性能

## 技术要求

### 1. 数据库
- **数据库**：MySQL 5.7
- **连接信息**：
  - 用户名：root
  - 密码：root
  - 环境：Windows 本地开发
- **数据库设计**：基于现有 docs 文件夹中的数据库结构
- **约束**：尽量不修改现有数据库结构
- **ORM**：不使用 Prisma

### 2. 默认账号
- **超级管理员**：
  - 用户名：admin
  - 密码：admin

## 页面结构

### 1. 前台页面
- 首页（瀑布流展示）
- 分类页面
- 资源详情页
- 用户注册/登录页
- 个人信息页

### 2. 后台管理页面
- 管理员登录页
- 资源管理页
- 分类管理页
- 用户管理页（超级管理员，普通管理员）
- 权限设置页（超级管理员）
- 操作日志页（超级管理员）

---

**注意事项**：
1. 开发环境为 Windows
2. 基于现有数据库结构，避免大幅修改
3. 注重UI/UX设计的现代感和优雅性
4. 集成丰富的动效和交互体验

