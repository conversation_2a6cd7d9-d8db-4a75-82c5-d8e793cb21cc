import { NextRequest, NextResponse } from 'next/server';
import { query, queryOne } from '@/lib/database';
import { createApiResponse, createApiError, withAuth } from '@/lib/utils';
import { hashPassword } from '@/lib/utils/server';

// 获取单个用户信息
export const GET = withAuth(async (request: NextRequest & { user: any }, { params }: { params: { id: string } }) => {
  try {
    const userId = parseInt(params.id);
    
    const user = await queryOne(`
      SELECT 
        u.id,
        u.username,
        u.email,
        u.role_id,
        r.name as role,
        u.avatar_media_id,
        u.created_at,
        u.updated_at,
        (SELECT COUNT(*) FROM activity_logs WHERE user_id = u.id) as action_count
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.id
      WHERE u.id = ?
    `, [userId]);

    if (!user) {
      return createApiError('USER_NOT_FOUND', 'User not found', null, 404);
    }

    return createApiResponse(true, user);
    
  } catch (error) {
    console.error('Get user error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'Failed to fetch user', error, 500);
  }
});

// 更新用户
export const PUT = withAuth(async (request: NextRequest & { user: any }, { params }: { params: { id: string } }) => {
  try {
    const userId = parseInt(params.id);
    const body = await request.json();
    const { username, email, password, role } = body;

    // 验证必填字段
    if (!username || !email || !role) {
      return createApiError('VALIDATION_ERROR', 'Username, email, and role are required', null, 400);
    }

    // 检查用户名和邮箱是否已被其他用户使用
    const existingUser = await queryOne(
      'SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?',
      [username, email, userId]
    );
    
    if (existingUser) {
      return createApiError('USER_ALREADY_EXISTS', 'Username or email already exists', null, 400);
    }

    // 获取角色ID
    const roleQuery = await queryOne('SELECT id FROM roles WHERE name = ?', [role]);
    if (!roleQuery) {
      return createApiError('INVALID_ROLE', 'Invalid role', null, 400);
    }

    // 构建更新查询
    const updateFields: string[] = [];
    const updateParams: any[] = [];

    updateFields.push('username = ?', 'email = ?', 'role_id = ?', 'updated_at = NOW()');
    updateParams.push(username, email, roleQuery.id);

    // 如果提供了密码，则更新密码
    if (password && password.trim()) {
      const hashedPassword = await hashPassword(password);
      updateFields.push('password = ?');
      updateParams.push(hashedPassword);
    }

    // 添加用户ID到参数末尾
    updateParams.push(userId);

    // 执行更新
    await query(
      `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`,
      updateParams
    );

    // 获取更新后的用户信息
    const updatedUser = await queryOne(`
      SELECT 
        u.id,
        u.username,
        u.email,
        u.role_id,
        r.name as role,
        u.avatar_media_id,
        u.created_at,
        u.updated_at,
        (SELECT COUNT(*) FROM activity_logs WHERE user_id = u.id) as action_count
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.id
      WHERE u.id = ?
    `, [userId]);

    // 记录操作日志
    await query(
      'INSERT INTO activity_logs (user_id, action, target_type, target_id, created_at) VALUES (?, ?, ?, ?, NOW())',
      [request.user.id, 'user_update', 'user', userId]
    );

    return createApiResponse(true, updatedUser, 'User updated successfully');
    
  } catch (error) {
    console.error('Update user error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'Failed to update user', error, 500);
  }
});

// 删除用户
export const DELETE = withAuth(async (request: NextRequest & { user: any }, { params }: { params: { id: string } }) => {
  try {
    const userId = parseInt(params.id);
    
    // 检查用户是否存在
    const existingUser = await queryOne('SELECT id, username FROM users WHERE id = ?', [userId]);
    if (!existingUser) {
      return createApiError('USER_NOT_FOUND', 'User not found', null, 404);
    }

    // 不能删除自己
    if (userId === request.user.id) {
      return createApiError('CANNOT_DELETE_SELF', 'Cannot delete your own account', null, 400);
    }

    // 删除用户
    await query('DELETE FROM users WHERE id = ?', [userId]);

    // 记录操作日志
    await query(
      'INSERT INTO activity_logs (user_id, action, target_type, target_id, created_at) VALUES (?, ?, ?, ?, NOW())',
      [request.user.id, 'user_delete', 'user', userId]
    );

    return createApiResponse(true, { id: userId }, 'User deleted successfully');
    
  } catch (error) {
    console.error('Delete user error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'Failed to delete user', error, 500);
  }
});