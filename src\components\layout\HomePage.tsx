'use client';

import { useState, useEffect, useMemo, useRef } from 'react';
import { Plus, Star, Clock, TrendingUp, Users, Search, Sparkles, Zap, Globe, ArrowRight } from 'lucide-react';
import LinkCard from '@/components/features/LinkCard';
import CategoryFilter from '@/components/features/CategoryFilter';
import SearchBar from '@/components/features/SearchBar';
import AuthModal from '@/components/layout/AuthModal';
import SideFloatingNav from '@/components/layout/SideFloatingNav';
import { VirtualWebsiteList } from '@/components/ui/VirtualList';
import { useSmartPrefetch, useAdaptivePrefetch, warmupApplication } from '@/lib/prefetch';

interface Website {
  id: number;
  title: string;
  url: string;
  description: string;
  category_id: number;
  category_name: string;
  is_private: boolean;
  icon_media_id?: string;
  created_at: string;
  updated_at: string;
}

interface Category {
  id: number;
  name: string;
  order: number;
  is_private: boolean;
  links_count: number;
}

interface HomePageProps {
  initialLinks: Website[];
  initialCategories: Category[];
}

export default function HomePage({ initialLinks, initialCategories }: HomePageProps) {
  const [websites, setWebsites] = useState<Website[]>(initialLinks);
  const [categories, setCategories] = useState<Category[]>(initialCategories);
  const [filteredWebsites, setFilteredWebsites] = useState<Website[]>(initialLinks);
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');
  const [user, setUser] = useState<any>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [animationStage, setAnimationStage] = useState(0);
  const heroRef = useRef<HTMLDivElement>(null);

  // 预取策略Hooks
  const { prefetchCategoryContent, prefetchRelatedContent } = useSmartPrefetch();
  const { recordBehavior } = useAdaptivePrefetch();

  // 页面加载动画
  useEffect(() => {
    setIsLoaded(true);
    const stages = [0, 1, 2, 3];
    stages.forEach((stage, index) => {
      setTimeout(() => setAnimationStage(stage), index * 200);
    });
  }, []);

  // 检查用户登录状态
  useEffect(() => {
    checkUserStatus();
  }, []);

  // 加载分类：登录后可获取“公开 + 本人私有（或超级管理员全部）”
  useEffect(() => {
    const controller = new AbortController();
    const loadCategories = async () => {
      try {
        const res = await fetch('/api/categories?limit=1000', {
          method: 'GET',
          credentials: 'include',
          headers: { 'Content-Type': 'application/json' },
          signal: controller.signal,
        });
        if (res.ok) {
          const json = await res.json();
          const list = json?.data?.categories || [];
          setCategories(Array.isArray(list) ? list : []);
        }
      } catch (e) {
        if ((e as any)?.name !== 'AbortError') {
          console.error('Load categories error:', e);
        }
      }
    };
    // 首次和登录状态变化后都刷新分类
    loadCategories();
    return () => controller.abort();
  }, [user]);

  // 应用启动预热
  useEffect(() => {
    warmupApplication();
  }, []);

  // 预取相关内容
  useEffect(() => {
    // 延迟预取，避免阻塞主要内容加载
    const timer = setTimeout(() => {
      prefetchRelatedContent();
    }, 2000);

    return () => clearTimeout(timer);
  }, [prefetchRelatedContent]);

  const checkUserStatus = async () => {
    try {
      const response = await fetch('/api/auth/me');
      if (response.ok) {
        const userData = await response.json();
        setUser(userData.data.user);
      }
    } catch (error) {
      console.error('Error checking user status:', error);
    }
  };

  // 过滤网站
  useEffect(() => {
    let filtered = websites;

    // 按分类过滤
    if (selectedCategory !== null) {
      filtered = filtered.filter(website => website.category_id === selectedCategory);
    }

    // 按搜索关键词过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(website => 
        (website.title && website.title.toLowerCase().includes(query)) ||
        (website.description && website.description.toLowerCase().includes(query)) ||
        (website.url && website.url.toLowerCase().includes(query))
      );
    }

    setFilteredWebsites(filtered);
  }, [websites, selectedCategory, searchQuery]);

  const handleCategorySelect = (categoryId: number | null) => {
    setSelectedCategory(categoryId);
    
    // 记录用户行为
    if (categoryId !== null) {
      recordBehavior('category', categoryId.toString());
      
      // 预取分类内容
      prefetchCategoryContent(categoryId.toString(), 300);
    }
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    
    // 记录搜索行为
    if (query.trim()) {
      recordBehavior('search', query);
    }
  };

  const openAuthModal = (mode: 'login' | 'register') => {
    setAuthMode(mode);
    setIsAuthModalOpen(true);
  };

  const closeAuthModal = () => {
    setIsAuthModalOpen(false);
  };

  // 性能优化：当网站数量超过50个时使用虚拟滚动
  const shouldUseVirtualization = filteredWebsites.length > 50;
  const virtualListHeight = useMemo(() => {
    if (typeof window !== 'undefined') {
      return Math.min(window.innerHeight * 0.8, 800);
    }
    return 600;
  }, []);
  const totalWebsites = initialLinks.length;
  const totalCategories = initialCategories.length;
  const recentWebsites = initialLinks.filter(website => {
    try {
      const websiteDate = new Date(website.created_at);
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return websiteDate > weekAgo;
    } catch {
      return false;
    }
  }).length;

  return (
    <div className="min-h-screen bg-background overflow-hidden">
      <SideFloatingNav user={user} />

      {/* 现代化Hero区域 */}
      <div
        ref={heroRef}
        className="relative min-h-screen hero-gradient flex items-center justify-center overflow-hidden"
      >
        {/* 动态背景装饰 */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/10 rounded-full blur-3xl animate-float"></div>
          <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-white/5 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-white/5 rounded-full blur-2xl animate-pulse"></div>
        </div>

        <div className="relative z-10 container mx-auto px-4 text-center">
          {/* 主标题 */}
          <div className={`transition-all duration-1000 ${animationStage >= 0 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 mb-6 text-white/80 text-sm">
              <Sparkles className="w-4 h-4" />
              <span>发现优质网站资源</span>
            </div>

            <h1 className="text-4xl sm:text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
              探索无限
              <span className="block text-gradient bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                创意世界
              </span>
            </h1>

            <p className="text-xl sm:text-2xl text-white/80 max-w-3xl mx-auto mb-8 leading-relaxed">
              精心策划的网站资源集合，为您的创作和工作提供无限灵感
            </p>
          </div>

          {/* 统计数据 */}
          <div className={`flex justify-center items-center gap-8 sm:gap-16 mb-12 transition-all duration-1000 delay-300 ${animationStage >= 1 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="text-center group cursor-pointer">
              <div className="text-3xl sm:text-4xl font-bold text-white mb-2 group-hover:scale-110 transition-transform duration-300">
                {totalWebsites}
              </div>
              <div className="text-white/60 text-sm sm:text-base">精选资源</div>
            </div>
            <div className="w-px h-12 bg-white/20"></div>
            <div className="text-center group cursor-pointer">
              <div className="text-3xl sm:text-4xl font-bold text-white mb-2 group-hover:scale-110 transition-transform duration-300">
                {totalCategories}
              </div>
              <div className="text-white/60 text-sm sm:text-base">分类目录</div>
            </div>
            <div className="w-px h-12 bg-white/20"></div>
            <div className="text-center group cursor-pointer">
              <div className="text-3xl sm:text-4xl font-bold text-white mb-2 group-hover:scale-110 transition-transform duration-300">
                {recentWebsites}
              </div>
              <div className="text-white/60 text-sm sm:text-base">本周新增</div>
            </div>
          </div>

          {/* CTA按钮组 */}
          <div className={`flex flex-col sm:flex-row gap-4 justify-center items-center mb-16 transition-all duration-1000 delay-500 ${animationStage >= 2 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <button
              onClick={() => {
                const contentSection = document.getElementById('content-section');
                contentSection?.scrollIntoView({ behavior: 'smooth' });
              }}
              className="btn-modern flex items-center gap-2 text-lg px-8 py-4"
            >
              <Globe className="w-5 h-5" />
              开始探索
              <ArrowRight className="w-5 h-5" />
            </button>

            {!user && (
              <button
                onClick={() => openAuthModal('register')}
                className="glass-effect text-white border border-white/20 rounded-lg px-8 py-4 text-lg font-semibold hover:bg-white/10 transition-all duration-300 flex items-center gap-2"
              >
                <Zap className="w-5 h-5" />
                立即加入
              </button>
            )}
          </div>

          {/* 滚动提示 */}
          <div className={`transition-all duration-1000 delay-700 ${animationStage >= 3 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="animate-bounce">
              <div className="w-6 h-10 border-2 border-white/30 rounded-full mx-auto flex justify-center">
                <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div id="content-section" className="relative bg-background">
        {/* 搜索区域 - 现代化设计 */}
        <div className="relative -mt-20 z-20 mb-16">
          <div className="container mx-auto px-4 max-w-4xl">
            <div className="glass-effect rounded-3xl p-8 shadow-2xl">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-foreground mb-2">寻找灵感</h2>
                <p className="text-muted-foreground">搜索您需要的资源或浏览分类</p>
              </div>

              <div className="relative mb-6">
                <SearchBar onSearch={handleSearch} />
              </div>

              {/* 热门搜索标签 */}
              <div className="flex flex-wrap justify-center gap-2">
                {['设计工具', '开发资源', '学习平台', '效率工具', '创意灵感'].map((tag) => (
                  <button
                    key={tag}
                    onClick={() => handleSearch(tag)}
                    className="category-tag text-sm"
                  >
                    {tag}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="container mx-auto px-4 max-w-7xl">
          {/* 分类筛选 - 现代化标签云 */}
          <div className="mb-12">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-foreground mb-4">探索分类</h2>
              <p className="text-muted-foreground text-lg">按类别发现精彩内容</p>
            </div>

            <div className="flex flex-wrap justify-center gap-3 mb-8">
              <button
                onClick={() => handleCategorySelect(null)}
                className={`category-tag ${selectedCategory === null ? 'active' : ''}`}
              >
                全部资源
              </button>
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => handleCategorySelect(category.id)}
                  className={`category-tag ${selectedCategory === category.id ? 'active' : ''}`}
                >
                  {category.name}
                  <span className="ml-2 text-xs opacity-70">({category.links_count})</span>
                </button>
              ))}
            </div>
          </div>

          {/* 网站资源展示 - 现代化网格 */}
          <div className="mb-16">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8 space-y-4 lg:space-y-0">
              <div>
                <h2 className="text-3xl font-bold text-foreground mb-2">
                  {selectedCategory
                    ? `${categories.find(c => c.id === selectedCategory)?.name}`
                    : searchQuery
                    ? `搜索结果`
                    : '精选资源'
                  }
                </h2>
                <p className="text-muted-foreground">
                  {selectedCategory
                    ? `发现 ${categories.find(c => c.id === selectedCategory)?.name} 相关的优质资源`
                    : searchQuery
                    ? `为 "${searchQuery}" 找到 ${filteredWebsites.length} 个结果`
                    : '精心挑选的优质网站资源'
                  }
                </p>
              </div>

              <div className="flex items-center space-x-4">
                <div className="text-sm text-muted-foreground">
                  共 {filteredWebsites.length} 个资源
                </div>
                {!user && (
                  <button
                    onClick={() => openAuthModal('register')}
                    className="btn-modern text-sm px-4 py-2"
                  >
                    解锁全部资源
                  </button>
                )}
              </div>
            </div>

            {/* 现代化资源网格 */}
            {shouldUseVirtualization ? (
              <div className="glass-effect rounded-2xl p-6">
                <div className="text-center mb-6">
                  <div className="inline-flex items-center gap-2 bg-primary/10 text-primary rounded-full px-4 py-2 text-sm">
                    <Zap className="w-4 h-4" />
                    <span>虚拟滚动已启用 - 优化性能</span>
                  </div>
                </div>
                <VirtualWebsiteList
                  websites={filteredWebsites}
                  height={virtualListHeight}
                  itemHeight={320}
                  className="rounded-xl overflow-hidden"
                />
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {filteredWebsites.map((website, index) => (
                  <div
                    key={website.id}
                    className="animate-fade-in-up"
                    style={{ animationDelay: `${index * 50}ms` }}
                  >
                    <LinkCard link={website} />
                  </div>
                ))}
              </div>
            )}

            {/* 现代化空状态 */}
            {filteredWebsites.length === 0 && (
              <div className="text-center py-20">
                <div className="relative mb-8">
                  <div className="w-32 h-32 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full flex items-center justify-center mx-auto mb-6 animate-pulse">
                    <Search className="w-16 h-16 text-primary" />
                  </div>
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-40 h-40 bg-primary/5 rounded-full blur-xl"></div>
                </div>

                <h3 className="text-2xl font-bold text-foreground mb-4">
                  {selectedCategory || searchQuery ? '未找到相关资源' : '暂无资源'}
                </h3>

                <p className="text-muted-foreground text-lg mb-8 max-w-lg mx-auto leading-relaxed">
                  {selectedCategory || searchQuery
                    ? '试试调整搜索条件，或者成为第一个推荐相关资源的人'
                    : '成为第一个分享优质资源的人，帮助社区发现更多精彩内容'
                  }
                </p>

                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                  {user ? (
                    <button className="btn-modern flex items-center gap-2">
                      <Plus className="w-5 h-5" />
                      推荐网站
                    </button>
                  ) : (
                    <button
                      onClick={() => openAuthModal('register')}
                      className="btn-modern flex items-center gap-2"
                    >
                      <Plus className="w-5 h-5" />
                      立即注册推荐
                    </button>
                  )}

                  <button
                    onClick={() => {
                      setSelectedCategory(null);
                      setSearchQuery('');
                    }}
                    className="glass-effect text-foreground border border-border rounded-lg px-6 py-3 font-semibold hover:bg-muted/50 transition-all duration-300"
                  >
                    浏览全部资源
                  </button>
                </div>
              </div>
            )}
        </div>

          {/* 热门分类展示 - 现代化卡片 */}
          {!selectedCategory && !searchQuery && (
            <div className="mb-20">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-foreground mb-4">热门分类</h2>
                <p className="text-muted-foreground text-lg">快速找到您感兴趣的内容</p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
                {categories.slice(0, 6).map((category, index) => (
                  <button
                    key={category.id}
                    onClick={() => handleCategorySelect(category.id)}
                    className="card-modern p-6 text-center group animate-fade-in-scale"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/60 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                      <TrendingUp className="w-6 h-6 text-white" />
                    </div>

                    <div className="text-lg font-bold text-foreground mb-2 group-hover:text-primary transition-colors">
                      {category.name}
                    </div>

                    <div className="text-sm text-muted-foreground mb-3">
                      {category.links_count} 个资源
                    </div>

                    <div className="w-full h-1 bg-muted rounded-full overflow-hidden">
                      <div
                        className="h-full bg-gradient-to-r from-primary to-primary/60 rounded-full transition-all duration-500 group-hover:w-full"
                        style={{ width: `${Math.min((category.links_count / Math.max(...categories.map(c => c.links_count))) * 100, 100)}%` }}
                      ></div>
                    </div>
                  </button>
                ))}
              </div>

              {categories.length > 6 && (
                <div className="text-center mt-8">
                  <button
                    onClick={() => {
                      const categorySection = document.querySelector('.category-tag');
                      categorySection?.scrollIntoView({ behavior: 'smooth' });
                    }}
                    className="glass-effect text-foreground border border-border rounded-lg px-6 py-3 font-semibold hover:bg-muted/50 transition-all duration-300 inline-flex items-center gap-2"
                  >
                    查看全部分类
                    <ArrowRight className="w-4 h-4" />
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* 认证弹窗 */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={closeAuthModal}
        mode={authMode}
        onModeChange={setAuthMode}
      />

      {/* 页面底部装饰 */}
      <div className="relative bg-gradient-to-t from-muted/20 to-transparent py-20">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-foreground mb-4">
              发现更多精彩内容
            </h3>
            <p className="text-muted-foreground mb-8">
              加入我们的社区，与千万用户一起探索互联网的无限可能
            </p>
            {!user && (
              <button
                onClick={() => openAuthModal('register')}
                className="btn-modern inline-flex items-center gap-2"
              >
                <Users className="w-5 h-5" />
                立即加入社区
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}