'use client';

import { useState, useEffect, useMemo, useRef } from 'react';
import { Plus, Star, Clock, TrendingUp, Users, Search, Sparkles, Zap, Globe, ArrowRight } from 'lucide-react';
import LinkCard from '@/components/features/LinkCard';
import CategoryFilter from '@/components/features/CategoryFilter';
import SearchBar from '@/components/features/SearchBar';
import AuthModal from '@/components/layout/AuthModal';
import SideFloatingNav from '@/components/layout/SideFloatingNav';
import { VirtualWebsiteList } from '@/components/ui/VirtualList';
import { useSmartPrefetch, useAdaptivePrefetch, warmupApplication } from '@/lib/prefetch';

interface Website {
  id: number;
  title: string;
  url: string;
  description: string;
  category_id: number;
  category_name: string;
  is_private: boolean;
  icon_media_id?: string;
  created_at: string;
  updated_at: string;
}

interface Category {
  id: number;
  name: string;
  order: number;
  is_private: boolean;
  links_count: number;
}

interface HomePageProps {
  initialLinks: Website[];
  initialCategories: Category[];
}

export default function HomePage({ initialLinks, initialCategories }: HomePageProps) {
  const [websites, setWebsites] = useState<Website[]>(initialLinks);
  const [categories, setCategories] = useState<Category[]>(initialCategories);
  const [filteredWebsites, setFilteredWebsites] = useState<Website[]>(initialLinks);
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');
  const [user, setUser] = useState<any>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [animationStage, setAnimationStage] = useState(0);
  const heroRef = useRef<HTMLDivElement>(null);

  // 预取策略Hooks
  const { prefetchCategoryContent, prefetchRelatedContent } = useSmartPrefetch();
  const { recordBehavior } = useAdaptivePrefetch();

  // 页面加载动画
  useEffect(() => {
    setIsLoaded(true);
    const stages = [0, 1, 2, 3];
    stages.forEach((stage, index) => {
      setTimeout(() => setAnimationStage(stage), index * 200);
    });
  }, []);

  // 检查用户登录状态
  useEffect(() => {
    checkUserStatus();
  }, []);

  // 加载分类：登录后可获取“公开 + 本人私有（或超级管理员全部）”
  useEffect(() => {
    const controller = new AbortController();
    const loadCategories = async () => {
      try {
        const res = await fetch('/api/categories?limit=1000', {
          method: 'GET',
          credentials: 'include',
          headers: { 'Content-Type': 'application/json' },
          signal: controller.signal,
        });
        if (res.ok) {
          const json = await res.json();
          const list = json?.data?.categories || [];
          setCategories(Array.isArray(list) ? list : []);
        }
      } catch (e) {
        if ((e as any)?.name !== 'AbortError') {
          console.error('Load categories error:', e);
        }
      }
    };
    // 首次和登录状态变化后都刷新分类
    loadCategories();
    return () => controller.abort();
  }, [user]);

  // 应用启动预热
  useEffect(() => {
    warmupApplication();
  }, []);

  // 预取相关内容
  useEffect(() => {
    // 延迟预取，避免阻塞主要内容加载
    const timer = setTimeout(() => {
      prefetchRelatedContent();
    }, 2000);

    return () => clearTimeout(timer);
  }, [prefetchRelatedContent]);

  const checkUserStatus = async () => {
    try {
      const response = await fetch('/api/auth/me');
      if (response.ok) {
        const userData = await response.json();
        setUser(userData.data.user);
      }
    } catch (error) {
      console.error('Error checking user status:', error);
    }
  };

  // 过滤网站
  useEffect(() => {
    let filtered = websites;

    // 按分类过滤
    if (selectedCategory !== null) {
      filtered = filtered.filter(website => website.category_id === selectedCategory);
    }

    // 按搜索关键词过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(website => 
        (website.title && website.title.toLowerCase().includes(query)) ||
        (website.description && website.description.toLowerCase().includes(query)) ||
        (website.url && website.url.toLowerCase().includes(query))
      );
    }

    setFilteredWebsites(filtered);
  }, [websites, selectedCategory, searchQuery]);

  const handleCategorySelect = (categoryId: number | null) => {
    setSelectedCategory(categoryId);
    
    // 记录用户行为
    if (categoryId !== null) {
      recordBehavior('category', categoryId.toString());
      
      // 预取分类内容
      prefetchCategoryContent(categoryId.toString(), 300);
    }
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    
    // 记录搜索行为
    if (query.trim()) {
      recordBehavior('search', query);
    }
  };

  const openAuthModal = (mode: 'login' | 'register') => {
    setAuthMode(mode);
    setIsAuthModalOpen(true);
  };

  const closeAuthModal = () => {
    setIsAuthModalOpen(false);
  };

  // 性能优化：当网站数量超过50个时使用虚拟滚动
  const shouldUseVirtualization = filteredWebsites.length > 50;
  const virtualListHeight = useMemo(() => {
    if (typeof window !== 'undefined') {
      return Math.min(window.innerHeight * 0.8, 800);
    }
    return 600;
  }, []);
  const totalWebsites = initialLinks.length;
  const totalCategories = initialCategories.length;
  const recentWebsites = initialLinks.filter(website => {
    try {
      const websiteDate = new Date(website.created_at);
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return websiteDate > weekAgo;
    } catch {
      return false;
    }
  }).length;

  return (
    <div className="min-h-screen bg-background">
      <SideFloatingNav user={user} />

      {/* 紧凑顶部区域 */}
      <div className="sticky top-0 z-40 glass-effect border-b border-border/20">
        <div className="container mx-auto px-4 py-4">
          {/* 顶部标题和统计 */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-4">
            <div>
              <h1 className="text-2xl font-bold text-foreground mb-1">
                资源导航
              </h1>
              <div className="flex items-center gap-6 text-sm text-muted-foreground">
                <span className="flex items-center gap-1">
                  <Globe className="w-4 h-4" />
                  {totalWebsites} 个资源
                </span>
                <span className="flex items-center gap-1">
                  <TrendingUp className="w-4 h-4" />
                  {totalCategories} 个分类
                </span>
                <span className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  本周新增 {recentWebsites}
                </span>
              </div>
            </div>

            {!user && (
              <button
                onClick={() => openAuthModal('register')}
                className="btn-modern text-sm px-4 py-2 flex items-center gap-2"
              >
                <Users className="w-4 h-4" />
                加入社区
              </button>
            )}
          </div>

          {/* 搜索栏 */}
          <div className="max-w-2xl mx-auto mb-4">
            <SearchBar onSearch={handleSearch} />
          </div>

          {/* 分类筛选 - 横向滚动 */}
          <div className="flex items-center gap-3 overflow-x-auto pb-2 scrollbar-hide">
            <button
              onClick={() => handleCategorySelect(null)}
              className={`category-tag whitespace-nowrap ${selectedCategory === null ? 'active' : ''}`}
            >
              全部
            </button>
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => handleCategorySelect(category.id)}
                className={`category-tag whitespace-nowrap ${selectedCategory === category.id ? 'active' : ''}`}
              >
                {category.name}
                <span className="ml-1 text-xs opacity-70">({category.links_count})</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* 主要内容区域 - 瀑布流资源展示 */}
      <div className="container mx-auto px-4 py-6">
        {/* 结果信息 */}
        {(selectedCategory || searchQuery) && (
          <div className="mb-6 text-center">
            <h2 className="text-xl font-semibold text-foreground mb-2">
              {selectedCategory
                ? `${categories.find(c => c.id === selectedCategory)?.name} 相关资源`
                : `搜索 "${searchQuery}" 的结果`
              }
            </h2>
            <p className="text-muted-foreground">
              找到 {filteredWebsites.length} 个相关资源
            </p>
          </div>
        )}

        {/* 瀑布流资源展示 */}
        {shouldUseVirtualization ? (
          <div className="glass-effect rounded-2xl p-6 mb-8">
            <div className="text-center mb-6">
              <div className="inline-flex items-center gap-2 bg-primary/10 text-primary rounded-full px-4 py-2 text-sm">
                <Zap className="w-4 h-4" />
                <span>虚拟滚动已启用 - 优化性能</span>
              </div>
            </div>
            <VirtualWebsiteList
              websites={filteredWebsites}
              height={virtualListHeight}
              itemHeight={320}
              className="rounded-xl overflow-hidden"
            />
          </div>
        ) : (
          <div className="masonry-grid">
            {filteredWebsites.map((website, index) => (
              <div
                key={website.id}
                className="masonry-item animate-fade-in-up"
                style={{ animationDelay: `${index * 50}ms` }}
              >
                <LinkCard link={website} />
              </div>
            ))}
          </div>
        )}

        {/* 空状态 */}
        {filteredWebsites.length === 0 && (
          <div className="text-center py-20">
            <div className="relative mb-8">
              <div className="w-24 h-24 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full flex items-center justify-center mx-auto mb-6 animate-pulse">
                <Search className="w-12 h-12 text-primary" />
              </div>
            </div>

            <h3 className="text-xl font-bold text-foreground mb-4">
              {selectedCategory || searchQuery ? '未找到相关资源' : '暂无资源'}
            </h3>

            <p className="text-muted-foreground mb-8 max-w-md mx-auto">
              {selectedCategory || searchQuery
                ? '试试调整搜索条件，或者推荐相关资源'
                : '成为第一个分享优质资源的人'
              }
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              {user ? (
                <button className="btn-modern flex items-center gap-2">
                  <Plus className="w-5 h-5" />
                  推荐网站
                </button>
              ) : (
                <button
                  onClick={() => openAuthModal('register')}
                  className="btn-modern flex items-center gap-2"
                >
                  <Plus className="w-5 h-5" />
                  立即注册
                </button>
              )}

              <button
                onClick={() => {
                  setSelectedCategory(null);
                  setSearchQuery('');
                }}
                className="glass-effect text-foreground border border-border rounded-lg px-6 py-3 font-semibold hover:bg-muted/50 transition-all duration-300"
              >
                浏览全部
              </button>
            </div>
          </div>
        )}
        </div>

      </div>
      </div>

      {/* 认证弹窗 */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={closeAuthModal}
        mode={authMode}
        onModeChange={setAuthMode}
      />
    </div>
  );
}