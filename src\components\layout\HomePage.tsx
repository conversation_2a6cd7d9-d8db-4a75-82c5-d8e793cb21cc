'use client';

import { useState, useEffect, useMemo } from 'react';
import { Plus, Star, Clock, TrendingUp, Users, Search } from 'lucide-react';
import LinkCard from '@/components/features/LinkCard';
import CategoryFilter from '@/components/features/CategoryFilter';
import SearchBar from '@/components/features/SearchBar';
import AuthModal from '@/components/layout/AuthModal';
import SideFloatingNav from '@/components/layout/SideFloatingNav';
import { VirtualWebsiteList } from '@/components/ui/VirtualList';
import { useSmartPrefetch, useAdaptivePrefetch, warmupApplication } from '@/lib/prefetch';

interface Website {
  id: number;
  title: string;
  url: string;
  description: string;
  category_id: number;
  category_name: string;
  is_private: boolean;
  icon_media_id?: string;
  created_at: string;
  updated_at: string;
}

interface Category {
  id: number;
  name: string;
  order: number;
  is_private: boolean;
  links_count: number;
}

interface HomePageProps {
  initialLinks: Website[];
  initialCategories: Category[];
}

export default function HomePage({ initialLinks, initialCategories }: HomePageProps) {
  const [websites, setWebsites] = useState<Website[]>(initialLinks);
  const [categories, setCategories] = useState<Category[]>(initialCategories);
  const [filteredWebsites, setFilteredWebsites] = useState<Website[]>(initialLinks);
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');
  const [user, setUser] = useState<any>(null);

  // 预取策略Hooks
  const { prefetchCategoryContent, prefetchRelatedContent } = useSmartPrefetch();
  const { recordBehavior } = useAdaptivePrefetch();

  // 检查用户登录状态
  useEffect(() => {
    checkUserStatus();
  }, []);

  // 加载分类：登录后可获取“公开 + 本人私有（或超级管理员全部）”
  useEffect(() => {
    const controller = new AbortController();
    const loadCategories = async () => {
      try {
        const res = await fetch('/api/categories?limit=1000', {
          method: 'GET',
          credentials: 'include',
          headers: { 'Content-Type': 'application/json' },
          signal: controller.signal,
        });
        if (res.ok) {
          const json = await res.json();
          const list = json?.data?.categories || [];
          setCategories(Array.isArray(list) ? list : []);
        }
      } catch (e) {
        if ((e as any)?.name !== 'AbortError') {
          console.error('Load categories error:', e);
        }
      }
    };
    // 首次和登录状态变化后都刷新分类
    loadCategories();
    return () => controller.abort();
  }, [user]);

  // 应用启动预热
  useEffect(() => {
    warmupApplication();
  }, []);

  // 预取相关内容
  useEffect(() => {
    // 延迟预取，避免阻塞主要内容加载
    const timer = setTimeout(() => {
      prefetchRelatedContent();
    }, 2000);

    return () => clearTimeout(timer);
  }, [prefetchRelatedContent]);

  const checkUserStatus = async () => {
    try {
      const response = await fetch('/api/auth/me');
      if (response.ok) {
        const userData = await response.json();
        setUser(userData.data.user);
      }
    } catch (error) {
      console.error('Error checking user status:', error);
    }
  };

  // 过滤网站
  useEffect(() => {
    let filtered = websites;

    // 按分类过滤
    if (selectedCategory !== null) {
      filtered = filtered.filter(website => website.category_id === selectedCategory);
    }

    // 按搜索关键词过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(website => 
        (website.title && website.title.toLowerCase().includes(query)) ||
        (website.description && website.description.toLowerCase().includes(query)) ||
        (website.url && website.url.toLowerCase().includes(query))
      );
    }

    setFilteredWebsites(filtered);
  }, [websites, selectedCategory, searchQuery]);

  const handleCategorySelect = (categoryId: number | null) => {
    setSelectedCategory(categoryId);
    
    // 记录用户行为
    if (categoryId !== null) {
      recordBehavior('category', categoryId.toString());
      
      // 预取分类内容
      prefetchCategoryContent(categoryId.toString(), 300);
    }
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    
    // 记录搜索行为
    if (query.trim()) {
      recordBehavior('search', query);
    }
  };

  const openAuthModal = (mode: 'login' | 'register') => {
    setAuthMode(mode);
    setIsAuthModalOpen(true);
  };

  const closeAuthModal = () => {
    setIsAuthModalOpen(false);
  };

  // 性能优化：当网站数量超过50个时使用虚拟滚动
  const shouldUseVirtualization = filteredWebsites.length > 50;
  const virtualListHeight = useMemo(() => {
    if (typeof window !== 'undefined') {
      return Math.min(window.innerHeight * 0.8, 800);
    }
    return 600;
  }, []);
  const totalWebsites = initialLinks.length;
  const totalCategories = initialCategories.length;
  const recentWebsites = initialLinks.filter(website => {
    try {
      const websiteDate = new Date(website.created_at);
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return websiteDate > weekAgo;
    } catch {
      return false;
    }
  }).length;

  return (
    <div className="min-h-screen bg-background">
      <SideFloatingNav user={user} />
      
      {/* 简化的欢迎区域 */}
      <div className="relative pt-6 pb-8 sm:pt-8 sm:pb-12 px-4">
        <div className="container mx-auto max-w-7xl">
          {/* 简洁的标题区域 */}
          <div className="text-center mb-6 sm:mb-8">
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-foreground mb-2 sm:mb-3 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              发现优质网站资源
            </h1>
            <p className="text-muted-foreground text-base sm:text-lg max-w-2xl mx-auto px-4">
              精选网络资源，提升工作效率
            </p>
          </div>
          
          {/* 快速统计 - 移动端优化 */}
          <div className="flex justify-center space-x-6 sm:space-x-12 mb-6 sm:mb-8">
            <div className="text-center group">
              <div className="text-xl sm:text-2xl font-bold text-primary group-hover:scale-110 transition-transform duration-300">{totalWebsites}</div>
              <div className="text-xs sm:text-sm text-muted-foreground">网站资源</div>
            </div>
            <div className="text-center group">
              <div className="text-xl sm:text-2xl font-bold text-primary group-hover:scale-110 transition-transform duration-300">{totalCategories}</div>
              <div className="text-xs sm:text-sm text-muted-foreground">分类目录</div>
            </div>
            <div className="text-center group">
              <div className="text-xl sm:text-2xl font-bold text-primary group-hover:scale-110 transition-transform duration-300">{recentWebsites}</div>
              <div className="text-xs sm:text-sm text-muted-foreground">本周新增</div>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="container mx-auto px-4 max-w-7xl">
        {/* 搜索区域 - 更突出 */}
        <div className="mb-6 sm:mb-8">
          <div className="max-w-2xl mx-auto">
            <SearchBar onSearch={handleSearch} />
          </div>
        </div>
        
        {/* 分类筛选 */}
        <div className="mb-6 sm:mb-8">
          <CategoryFilter 
            categories={categories} 
            onCategorySelect={handleCategorySelect}
          />
        </div>

        {/* 网站资源展示 - 主要内容 */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 sm:mb-6 space-y-3 sm:space-y-0">
            <h2 className="text-xl sm:text-2xl font-bold text-foreground">
              {selectedCategory 
                ? `${categories.find(c => c.id === selectedCategory)?.name} 相关资源`
                : searchQuery 
                ? `搜索 "${searchQuery}" 的结果`
                : '精选资源'
              }
            </h2>
            <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0 text-sm text-muted-foreground">
              <span>共 {filteredWebsites.length} 个{user ? '' : '公开'}资源</span>
              {!user && (
                <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-2 space-y-1 sm:space-y-0">
                  <span className="text-xs text-muted-foreground">仅显示公开资源</span>
                  <button 
                    onClick={() => openAuthModal('register')}
                    className="px-3 py-2 sm:px-4 bg-primary/10 text-primary rounded-lg hover:bg-primary/20 transition-colors text-xs sm:text-sm self-start sm:self-auto"
                  >
                    登录查看全部
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* 资源网格 - 性能优化：大量数据时使用虚拟滚动 */}
          {shouldUseVirtualization ? (
            <div className="mb-4">
              <div className="text-sm text-muted-foreground mb-4 text-center">
                显示 {filteredWebsites.length} 个资源（已启用虚拟滚动优化）
              </div>
              <VirtualWebsiteList
                websites={filteredWebsites}
                height={virtualListHeight}
                itemHeight={320}
                className="border border-border rounded-lg overflow-hidden"
              />
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
              {filteredWebsites.map((website) => (
                <LinkCard
                  key={website.id}
                  link={website}
                />
              ))}
            </div>
          )}

          {/* 空状态 */}
          {filteredWebsites.length === 0 && (
            <div className="text-center py-16">
              <div className="w-24 h-24 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-6 group hover:bg-muted transition-colors">
                <Search className="w-12 h-12 text-muted-foreground group-hover:scale-110 transition-transform duration-300" />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">
                {selectedCategory || searchQuery ? '未找到相关资源' : '暂无资源'}
              </h3>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                {selectedCategory || searchQuery 
                  ? '试试其他关键词或分类，或者为我们推荐优质资源'
                  : '成为第一个分享资源的人，帮助更多人发现优质内容'
                }
              </p>
              {user ? (
                <button className="px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors hover:scale-105 transform duration-200">
                  推荐网站
                </button>
              ) : (
                <button 
                  onClick={() => openAuthModal('register')}
                  className="px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors hover:scale-105 transform duration-200"
                >
                  立即注册推荐
                </button>
              )}
            </div>
          )}
        </div>

        {/* 热门分类快速导航 - 移到下方 */}
        <div className="mb-12">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-foreground">热门分类</h2>
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <div className="flex items-center space-x-1">
                <TrendingUp className="w-4 h-4" />
                <span>热门推荐</span>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {categories.slice(0, 6).map((category) => (
              <button
                key={category.id}
                onClick={() => handleCategorySelect(category.id)}
                className="group p-4 bg-card/50 border border-border/50 rounded-xl hover:border-primary/50 hover:bg-card transition-all duration-300 text-center hover:scale-105 transform"
              >
                <div className="text-base font-semibold text-foreground mb-1 group-hover:text-primary transition-colors">
                  {category.name}
                </div>
                <div className="text-sm text-muted-foreground">
                  {category.links_count} 个资源
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* 认证弹窗 */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={closeAuthModal}
        mode={authMode}
        onModeChange={setAuthMode}
      />
    </div>
  );
}