'use client';

import { useEffect, useMemo, useState } from 'react';
import { Button } from '@/components/ui/Button';
import { CrudModal, ConfirmDeleteModal } from '@/components/admin/CrudModal';
import { Badge } from '@/components/ui/Badge';
import { Plus, Edit, Trash2 } from 'lucide-react';
import { useToast } from '@/contexts/ToastContext';

interface Role { id: number; name: string; permission_count?: number; user_count?: number; permissions?: { id: number; name: string }[] }
interface Permission { id: number; name: string }

export default function PermissionsManagerClient({ isSuperAdmin, roles: initialRoles, permissions: initialPerms }: { isSuperAdmin: boolean; roles: Role[]; permissions: Permission[] }) {
  const { showSuccess, showError } = useToast();
  const [roles, setRoles] = useState<Role[]>(initialRoles);
  const [permissions] = useState<Permission[]>(initialPerms);
  const [loading, setLoading] = useState(false);

  // 中文映射：权限显示名称
  const displayName = (name: string) => {
    const map: Record<string, string> = {
      'admin:access': '管理员访问',
      'admin:dashboard:view': '查看仪表盘',
      'users:list': '用户列表',
      'users:create': '创建用户',
      'users:edit': '编辑用户',
      'users:update': '更新用户',
      'users:delete': '删除用户',
      'users:read': '查看用户',
      'users:view': '查看用户',
      'users:manage': '用户管理',
      'roles:list': '角色列表',
      'roles:create': '创建角色',
      'roles:edit': '编辑角色',
      'roles:update': '更新角色',
      'roles:delete': '删除角色',
      'roles:view': '查看角色',
      'roles:manage': '角色管理',
      'categories:list': '分类列表',
      'categories:create': '创建分类',
      'categories:edit': '编辑分类',
      'categories:update': '更新分类',
      'categories:delete': '删除分类',
      'categories:read': '查看分类',
      'categories:view': '查看分类',
      'categories:manage': '分类管理',
      'links:list': '链接列表',
      'links:create': '创建链接',
      'links:edit': '编辑链接',
      'links:update': '更新链接',
      'links:delete': '删除链接',
      'links:read': '查看链接',
      'links:view': '查看链接',
      'links:manage': '链接管理',
      'logs:view': '查看日志',
      'logs:read': '查看日志',
      'system:settings': '系统设置',
      'system:backup': '数据备份',
      'system:logs': '系统日志',
      'system:manage': '系统管理',
      'content:view': '查看内容',
      'content:create': '创建内容',
      'content:update': '编辑内容',
      'content:delete': '删除内容',
      'content:publish': '发布内容',
      'profile:view': '查看资料',
      'profile:edit': '编辑资料',
      'profile:password': '修改密码',
      'analytics:view': '查看统计',
      'analytics:export': '导出数据',
      'media:view': '查看媒体',
      'media:upload': '上传媒体',
      'media:delete': '删除媒体'
    };
    return map[name] || name;
  };

  const refreshRoles = async () => {
    try {
      const res = await fetch('/api/roles');
      if (res.ok) {
        const json = await res.json();
        setRoles(json?.data || []);
      }
    } catch {}
  };

  const [createOpen, setCreateOpen] = useState(false);
  const [editOpen, setEditOpen] = useState<Role | null>(null);
  const [deleteOpen, setDeleteOpen] = useState<Role | null>(null);
  const [currentEditData, setCurrentEditData] = useState<any>(null);

  const roleFields = useMemo(() => ([
    { name: 'name', label: '角色名称', type: 'text', required: true },
    { name: 'permissionNames', label: '权限', type: 'multiselect', required: false, options: initialPerms.map(p => ({ label: p.name, value: p.name })), allowSelectAll: true }
  ]), [initialPerms]);

  const handleCreate = async (data: Record<string, any>) => {
    setLoading(true);
    try {
      const permissionNames: string[] = Array.isArray(data.permissionNames) ? data.permissionNames : [];
      const res = await fetch('/api/roles', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ name: data.name, permissionNames }) });
      const json = await res.json();
      if (res.ok && json?.success) {
        await refreshRoles();
        showSuccess('创建成功', `角色“${data.name}”已创建`);
      } else {
        showError('创建失败', json?.error?.message || '创建角色失败');
      }
    } finally { setLoading(false); }
  };

  const handleUpdate = async (data: Record<string, any>) => {
    if (!editOpen) return;
    setLoading(true);
    try {
      const permissionNames: string[] = Array.isArray(data.permissionNames) ? data.permissionNames : [];
      const res = await fetch(`/api/roles/${editOpen.id}`, { method: 'PUT', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ name: data.name, permissionNames }) });
      const json = await res.json();
      if (res.ok && json?.success) {
        await refreshRoles();
        showSuccess('更新成功', `角色“${data.name}”已更新`);
      } else {
        showError('更新失败', json?.error?.message || '更新角色失败');
      }
    } finally { setLoading(false); setEditOpen(null); }
  };

  const handleDelete = async () => {
    if (!deleteOpen) return;
    setLoading(true);
    try {
      const res = await fetch(`/api/roles/${deleteOpen.id}`, { method: 'DELETE' });
      const json = await res.json();
      if (res.ok && json?.success) {
        await refreshRoles();
        showSuccess('删除成功', `角色“${deleteOpen.name}”已删除`);
      } else {
        const msg = json?.error?.code === 'ROLE_IN_USE' ? '该角色仍有关联用户，无法删除' : (json?.error?.message || '删除角色失败');
        showError('删除失败', msg);
      }
    } finally { setLoading(false); setDeleteOpen(null); }
  };

  if (!isSuperAdmin) return null;

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="text-sm text-muted-foreground">仅超级管理员可管理角色与权限</div>
        <Button onClick={() => setCreateOpen(true)}>
          <Plus className="w-4 h-4 mr-2" /> 新增角色
        </Button>
      </div>

      {/* 角色列表（可编辑/删除） */}
      <div className="space-y-3">
        {roles.map(role => (
          <div key={role.id} className="flex items-center justify-between p-3 border border-border rounded-lg">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-sm font-semibold">
                {role.name.charAt(0).toUpperCase()}
              </div>
              <div>
                <div className="font-medium text-foreground">{role.name === 'super_admin' ? '超级管理员' : role.name === 'admin' ? '管理员' : '用户'}</div>
                {Array.isArray((role as any).permissionNames) && (role as any).permissionNames.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-1">
                    {(role as any).permissionNames.map((pn: string) => (
                      <span key={pn} className="text-[11px] px-1.5 py-0.5 rounded border border-border text-muted-foreground">{displayName(pn)}</span>
                    ))}
                    {(!(role as any).permissionNames || (role as any).permissionNames.length === 0) && (
                      <span className="text-[11px] px-1.5 py-0.5 rounded border border-dashed text-muted-foreground">无权限</span>
                    )}
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {role.name !== 'super_admin' && (
                <>
                  <Button variant="outline" size="sm" onClick={() => {
                    setCurrentEditData({
                      name: role.name,
                      permissionNames: Array.isArray((role as any).permissionNames) ? (role as any).permissionNames : []
                    });
                    setEditOpen(role);
                  }}>
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => setDeleteOpen(role)}>
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </>
              )}
            </div>
          </div>
        ))}
      </div>

      <CrudModal
        isOpen={createOpen}
        onClose={() => setCreateOpen(false)}
        title="新增角色"
        fields={roleFields as any}
        onSubmit={handleCreate}
        loading={loading}
      />

      <CrudModal
        isOpen={!!editOpen}
        onClose={() => setEditOpen(null)}
        title="编辑角色"
        fields={roleFields as any}
        initialData={currentEditData || editOpen || undefined}
        onSubmit={handleUpdate}
        loading={loading}
      />

      <ConfirmDeleteModal
        isOpen={!!deleteOpen}
        onClose={() => setDeleteOpen(null)}
        onConfirm={handleDelete}
        title="删除角色"
        message={`确定删除角色“${deleteOpen?.name}”吗？`}
        loading={loading}
      />
    </div>
  );
}

