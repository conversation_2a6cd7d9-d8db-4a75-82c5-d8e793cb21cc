import { NextRequest, NextResponse } from 'next/server';
import { query, queryOne } from '@/lib/database';
import { createApiResponse, createApiError, withAuth } from '@/lib/utils';
import { generateCacheKey, withCache, invalidateCache, CACHE_TTL } from '@/lib/cache';

// 获取单个分类信息
export const GET = withAuth(async (request: NextRequest & { user: any }, { params }: { params: { id: string } }) => {
  try {
    const categoryId = parseInt(params.id);
    
    // 生成缓存键
    const cacheKey = generateCacheKey('category', { id: categoryId });
    
    // 使用缓存包装器
    const fetchCategoryData = withCache(
      cacheKey,
      CACHE_TTL.MEDIUM,
      async () => {
        const category = await queryOne(`
          SELECT 
            c.id,
            c.name,
            c.\`order\`,
            c.is_private,
            c.created_at,
            c.updated_at,
            COUNT(l.id) as links_count
          FROM categories c
          LEFT JOIN links l ON c.id = l.category_id
          WHERE c.id = ?
          GROUP BY c.id, c.name, c.\`order\`, c.is_private, c.created_at, c.updated_at
        `, [categoryId]);
        
        if (!category) {
          throw new Error('CATEGORY_NOT_FOUND');
        }
        
        return category;
      }
    );
    
    try {
      const category = await fetchCategoryData();
      return createApiResponse(true, category);
    } catch (error: any) {
      if (error.message === 'CATEGORY_NOT_FOUND') {
        return createApiError('CATEGORY_NOT_FOUND', '分类不存在', null, 404);
      }
      throw error;
    }
    
  } catch (error) {
    console.error('Get category error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', '获取分类失败', error, 500);
  }
});

// 更新分类
export const PUT = withAuth(async (request: NextRequest & { user: any }, { params }: { params: { id: string } }) => {
  try {
    const categoryId = parseInt(params.id);
    const body = await request.json();
    const { name, order, isPrivate } = body;

    // 验证必填字段
    if (!name) {
      return createApiError('VALIDATION_ERROR', '分类名称为必填项', null, 400);
    }

    // 检查分类名是否已被其他分类使用
    const existingCategory = await queryOne(
      'SELECT id FROM categories WHERE name = ? AND id != ?',
      [name, categoryId]
    );
    
    if (existingCategory) {
      return createApiError('CATEGORY_ALREADY_EXISTS', '分类名称已存在', null, 400);
    }

    // 检查分类是否存在
    const currentCategory = await queryOne('SELECT id FROM categories WHERE id = ?', [categoryId]);
    if (!currentCategory) {
      return createApiError('CATEGORY_NOT_FOUND', '分类不存在', null, 404);
    }

    // 执行更新
    await query(`
      UPDATE categories 
      SET name = ?, \`order\` = ?, is_private = ?, updated_at = NOW()
      WHERE id = ?
    `, [name, order || 0, isPrivate ? 1 : 0, categoryId]);

    // 获取更新后的分类信息
    const updatedCategory = await queryOne(`
      SELECT 
        c.id,
        c.name,
        c.\`order\`,
        c.is_private,
        c.created_at,
        c.updated_at,
        COUNT(l.id) as links_count
      FROM categories c
      LEFT JOIN links l ON c.id = l.category_id
      WHERE c.id = ?
      GROUP BY c.id, c.name, c.\`order\`, c.is_private, c.created_at, c.updated_at
    `, [categoryId]);

    // 记录操作日志
    await query(
      'INSERT INTO activity_logs (user_id, action, target_type, target_id, created_at) VALUES (?, ?, ?, ?, NOW())',
      [request.user.id, 'category_update', 'category', categoryId]
    );
    
    // 缓存失效 - 清除相关缓存
    invalidateCache.categories();

    return createApiResponse(true, updatedCategory, '分类更新成功');
    
  } catch (error) {
    console.error('Update category error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', '更新分类失败', error, 500);
  }
});

// 删除分类
export const DELETE = withAuth(async (request: NextRequest & { user: any }, { params }: { params: { id: string } }) => {
  try {
    const categoryId = parseInt(params.id);
    
    // 检查分类是否存在
    const existingCategory = await queryOne('SELECT id, name FROM categories WHERE id = ?', [categoryId]);
    if (!existingCategory) {
      return createApiError('CATEGORY_NOT_FOUND', '分类不存在', null, 404);
    }

    // 检查分类下是否有链接
    const linksCount = await queryOne('SELECT COUNT(*) as count FROM links WHERE category_id = ?', [categoryId]);
    if (linksCount.count > 0) {
      return createApiError('CATEGORY_HAS_LINKS', `该分类下有 ${linksCount.count} 个网站，请先移动或删除这些网站后再尝试删除。`, null, 400);
    }

    // 删除分类
    await query('DELETE FROM categories WHERE id = ?', [categoryId]);

    // 记录操作日志
    await query(
      'INSERT INTO activity_logs (user_id, action, target_type, target_id, created_at) VALUES (?, ?, ?, ?, NOW())',
      [request.user.id, 'category_delete', 'category', categoryId]
    );
    
    // 缓存失效 - 清除相关缓存
    invalidateCache.categories();

    return createApiResponse(true, { id: categoryId }, '分类删除成功');
    
  } catch (error) {
    console.error('Delete category error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', '删除分类失败', error, 500);
  }
});