import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { NextRequest, NextResponse } from 'next/server';
import { SignJWT, jwtVerify } from 'jose';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || 'your-secret-key');

export async function signJWT(payload: any, expiresIn: string = '1d'): Promise<string> {
  return new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime(expiresIn)
    .sign(JWT_SECRET);
}

export async function verifyJWT(token: string): Promise<any> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return payload;
  } catch (error) {
    throw new Error('Invalid token');
  }
}

export function getTokenFromRequest(request: NextRequest): string | null {
  if (!request) {
    return null;
  }
  
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  // 尝试从cookie中获取token
  const token = request.cookies.get('token')?.value;
  return token || null;
}

export async function getUserFromRequest(request: NextRequest) {
  try {
    if (!request) {
      console.warn('getUserFromRequest called without request object');
      return null;
    }
    
    const token = getTokenFromRequest(request);
    if (!token) {
      return null;
    }
    
    const payload = await verifyJWT(token);
    return payload;
  } catch (error) {
    console.error('Error verifying token:', error);
    return null;
  }
}

export function createApiResponse(
  success: boolean,
  data?: any,
  message?: string,
  code: number = 200
): NextResponse {
  const response = {
    success,
    data,
    message,
    code,
    timestamp: new Date().toISOString(),
  };

  return NextResponse.json(response, { status: code });
}

export function createApiError(
  code: string,
  message: string,
  details?: any,
  status: number = 400
): NextResponse {
  const response = {
    success: false,
    error: {
      code,
      message,
      details,
    },
    timestamp: new Date().toISOString(),
  };

  return NextResponse.json(response, { status });
}

export function withAuth(handler: (request: NextRequest & { user: any }, context?: any) => Promise<NextResponse>) {
  return async (request: NextRequest, context?: any) => {
    try {
      const user = await getUserFromRequest(request);
      if (!user) {
        return createApiError('UNAUTHORIZED', 'Authentication required', null, 401);
      }
      
      // 将用户信息附加到request对象上
      (request as any).user = user;
      
      return handler(request as NextRequest & { user: any }, context);
    } catch (error) {
      return createApiError('INTERNAL_SERVER_ERROR', 'Internal server error', error, 500);
    }
  };
}

export function withOptionalAuth(handler: (request: NextRequest & { user?: any }, context?: any) => Promise<NextResponse>) {
  return async (request: NextRequest, context?: any) => {
    try {
      let user = null;
      try {
        user = await getUserFromRequest(request);
      } catch (error) {
        // 忽略认证错误，继续处理请求
        console.log('Optional auth failed:', error);
      }
      
      // 将用户信息（可能为null）附加到request对象上
      (request as any).user = user;
      
      return handler(request as NextRequest & { user?: any }, context);
    } catch (error) {
      return createApiError('INTERNAL_SERVER_ERROR', 'Internal server error', error, 500);
    }
  };
}

export function withPermission(permission: string) {
  return function (handler: (request: NextRequest, context?: any, user?: any) => Promise<NextResponse>) {
    return withAuth(async (request: NextRequest, context?: any, user?: any) => {
      if (!user.permissions || !user.permissions.includes(permission)) {
        return createApiError('FORBIDDEN', 'Insufficient permissions', null, 403);
      }
      
      return handler(request, context, user);
    });
  };
}

export function withRole(role: string) {
  return function (handler: (request: NextRequest, context?: any, user?: any) => Promise<NextResponse>) {
    return withAuth(async (request: NextRequest, context?: any, user?: any) => {
      if (user.role !== role) {
        return createApiError('FORBIDDEN', 'Insufficient role permissions', null, 403);
      }
      
      return handler(request, context, user);
    });
  };
}

export async function validateRequest(request: NextRequest, schema: any): Promise<any> {
  try {
    const body = await request.json();
    return schema.parse(body);
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`Validation error: ${error.message}`);
    }
    throw new Error('Invalid request data');
  }
}

export function parsePaginationParams(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  
  return {
    page: parseInt(searchParams.get('page') || '1'),
    limit: parseInt(searchParams.get('limit') || '10'),
    search: searchParams.get('search') || '',
    sortBy: searchParams.get('sortBy') || 'created_at',
    sortOrder: (searchParams.get('sortOrder') || 'desc') as 'asc' | 'desc',
  };
}

export function createPaginationResponse<T>(
  data: T[],
  page: number,
  limit: number,
  total: number
) {
  const totalPages = Math.ceil(total / limit);
  
  return {
    data,
    pagination: {
      page,
      limit,
      total,
      totalPages,
    },
  };
}