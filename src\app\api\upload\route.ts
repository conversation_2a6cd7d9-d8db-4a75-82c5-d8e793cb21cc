import { NextRequest, NextResponse } from 'next/server';
import { insert } from '@/lib/database';
import { getUserFromRequest, createApiResponse, createApiError } from '@/lib/utils';
import { invalidateCache } from '@/lib/cache';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ALLOWED_FILE_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];

export async function POST(request: NextRequest) {
  try {
    const user = await getUserFromRequest(request);
    if (!user) {
      return createApiError('UNAUTHORIZED', 'Authentication required', null, 401);
    }

    const data = await request.formData();
    const file: File | null = data.get('file') as unknown as File;
    const type = data.get('type') as string || 'other';

    if (!file) {
      return createApiError('VALIDATION_ERROR', 'No file uploaded', null, 400);
    }

    if (file.size > MAX_FILE_SIZE) {
      return createApiError('FILE_SIZE_EXCEEDED', 'File size exceeds limit', null, 400);
    }

    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      return createApiError('INVALID_FILE_TYPE', 'Invalid file type', null, 400);
    }

    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    const fileName = `${uuidv4()}_${file.name}`;
    const uploadDir = join(process.cwd(), 'public', 'uploads', type);
    
    try {
      await mkdir(uploadDir, { recursive: true });
    } catch (error) {
      // Directory already exists
    }

    const filePath = join(uploadDir, fileName);
    await writeFile(filePath, buffer);

    const fileUrl = `/uploads/${type}/${fileName}`;

    const mediaId = await insert(
      'INSERT INTO media (file_path, file_type, file_size, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
      [fileUrl, file.type, file.size]
    );

    const responseData = {
      id: mediaId,
      fileName,
      originalName: file.name,
      fileSize: file.size,
      fileType: file.type,
      filePath: fileUrl,
      url: fileUrl,
      uploadedAt: new Date().toISOString()
    };

    // 缓存失效 - 清除相关缓存
    invalidateCache.media();

    return createApiResponse(true, responseData, 'File uploaded successfully', 201);

  } catch (error) {
    console.error('File upload error:', error);
    return createApiError('INTERNAL_SERVER_ERROR', 'File upload failed', error, 500);
  }
}