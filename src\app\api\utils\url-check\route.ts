import { NextRequest } from 'next/server';
import { createApiError, createApiResponse } from '@/lib/utils';

// 静默检测给定URL的可访问性
// 规则：
// - 输入允许无协议；将按 https:// 与 http:// 依次尝试
// - 先发 HEAD，请求被拒绝或 405 再退回 GET
// - 2xx/3xx 视为可访问；4xx/5xx 视为不可访问但返回状态信息
export const POST = async (request: NextRequest) => {
  try {
    const body = await request.json().catch(() => ({}));
    const rawUrl: string = (body?.url || '').toString().trim();

    if (!rawUrl) {
      return createApiError('VALIDATION_ERROR', 'URL必填', null, 400);
    }

    // 生成候选URL
    const hasScheme = /^[a-zA-Z][a-zA-Z0-9+.-]*:\/\//.test(rawUrl);
    const candidates = hasScheme ? [rawUrl] : [`https://${rawUrl}`, `http://${rawUrl}`];

    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), 6000);

    let lastError: any = null;

    for (const candidate of candidates) {
      try {
        // 先尝试 HEAD
        let res = await fetch(candidate, {
          method: 'HEAD',
          redirect: 'follow',
          signal: controller.signal,
          headers: {
            'User-Agent': 'Mozilla/5.0 (compatible; URLCheckBot/1.0; +https://example.com)'
          }
        });

        // 某些站点不支持HEAD，退回GET
        if (res.status === 405 || res.status === 403) {
          res = await fetch(candidate, {
            method: 'GET',
            redirect: 'follow',
            signal: controller.signal,
            headers: {
              'User-Agent': 'Mozilla/5.0 (compatible; URLCheckBot/1.0; +https://example.com)'
            }
          });
        }

        clearTimeout(timeout);

        const ok = res.status >= 200 && res.status < 400;
        return createApiResponse(true, {
          input: rawUrl,
          tried: candidate,
          reachable: ok,
          status: res.status,
          statusText: res.statusText
        }, ok ? '可访问' : '不可访问');
      } catch (err) {
        lastError = err;
        // 如果是超时/网络错误，尝试下一个候选
        continue;
      }
    }

    clearTimeout(timeout);

    return createApiResponse(true, {
      input: rawUrl,
      tried: candidates,
      reachable: false,
      error: lastError ? (lastError as any).message || '网络错误' : '网络错误'
    }, '不可访问');
  } catch (error) {
    return createApiError('INTERNAL_SERVER_ERROR', '检测失败', error, 500);
  }
};

